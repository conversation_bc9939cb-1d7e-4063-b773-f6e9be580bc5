import { Metadata } from 'next';
import { ServicesManagement } from '@/components/admin/services/services-management';

export const metadata: Metadata = {
  title: 'Services Management - Technoloway Admin',
  description: 'Manage service categories, services, options, and features in the Technoloway admin dashboard.',
  keywords: [
    'services management',
    'admin dashboard',
    'service categories',
    'service options',
    'option features',
    'technoloway admin',
    'content management',
    'service hierarchy'
  ],
  robots: {
    index: false,
    follow: false,
    googleBot: {
      index: false,
      follow: false,
    },
  },
  alternates: {
    canonical: '/admin-dashboard/services',
  },
};

export default function ServicesPage() {
  return (
    <div className="h-full bg-gradient-to-br from-gray-50 via-white to-blue-50/20">
      <div className="h-full px-4 sm:px-6 lg:px-8 py-4">
        <ServicesManagement />
      </div>
    </div>
  );
}
