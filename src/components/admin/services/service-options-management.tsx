'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  ListBulletIcon,
  MagnifyingGlassIcon,
  FunnelIcon
} from '@heroicons/react/24/outline'

interface Service {
  id: string
  categoryId: string
  name: string
  description: string
  iconClass?: string
  price: number
  discountRate?: number
  totalDiscount?: number
  manager?: string
  isActive: boolean
  displayOrder: number
  createdAt: string
  updatedAt: string
  category?: {
    id: string
    name: string
  }
  _count?: {
    serviceOptions: number
    orderDetails: number
  }
}

interface ServiceOption {
  id: string
  serviceId: string
  name: string
  description?: string
  price?: number
  discountRate?: number
  totalDiscount?: number
  isActive: boolean
  createdAt: string
  updatedAt: string
  service?: {
    id: string
    name: string
    category?: {
      id: string
      name: string
    }
  }
  _count?: {
    features: number
    orderDetails: number
  }
}

interface ServiceOptionsManagementProps {
  service: Service
  selectedOption: ServiceOption | null
  onOptionSelect: (option: ServiceOption | null) => void
}

interface ServiceOptionFormData {
  name: string
  description: string
  price: number
  discountRate: number
  isActive: boolean
}

export function ServiceOptionsManagement({ service, selectedOption, onOptionSelect }: ServiceOptionsManagementProps) {
  const [options, setOptions] = useState<ServiceOption[]>([])
  const [filteredOptions, setFilteredOptions] = useState<ServiceOption[]>([])
  const [loading, setLoading] = useState(true)
  const [isFormOpen, setIsFormOpen] = useState(false)
  const [editingOption, setEditingOption] = useState<ServiceOption | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('')
  const [viewMode, setViewMode] = useState<'table' | 'list'>('list')

  const [formData, setFormData] = useState<ServiceOptionFormData>({
    name: '',
    description: '',
    price: 0,
    discountRate: 0,
    isActive: true
  })

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm)
    }, 300)
    return () => clearTimeout(timer)
  }, [searchTerm])

  useEffect(() => {
    fetchOptions()
  }, [service.id])

  // Mock data for demonstration
  const fetchOptions = async () => {
    setLoading(true)
    try {
      // Mock data - replace with actual API call
      const mockOptions: ServiceOption[] = [
        {
          id: '1',
          serviceId: service.id,
          name: 'Basic Package',
          description: 'Essential features for small businesses',
          price: 1000,
          discountRate: 5,
          totalDiscount: 50,
          isActive: true,
          createdAt: '2024-01-15T10:00:00Z',
          updatedAt: '2024-01-15T10:00:00Z',
          service: { id: service.id, name: service.name, category: service.category },
          _count: { features: 5, orderDetails: 12 }
        },
        {
          id: '2',
          serviceId: service.id,
          name: 'Professional Package',
          description: 'Advanced features for growing businesses',
          price: 2500,
          discountRate: 10,
          totalDiscount: 250,
          isActive: true,
          createdAt: '2024-01-14T10:00:00Z',
          updatedAt: '2024-01-14T10:00:00Z',
          service: { id: service.id, name: service.name, category: service.category },
          _count: { features: 12, orderDetails: 8 }
        },
        {
          id: '3',
          serviceId: service.id,
          name: 'Enterprise Package',
          description: 'Complete solution for large organizations',
          price: 5000,
          discountRate: 15,
          totalDiscount: 750,
          isActive: true,
          createdAt: '2024-01-13T10:00:00Z',
          updatedAt: '2024-01-13T10:00:00Z',
          service: { id: service.id, name: service.name, category: service.category },
          _count: { features: 20, orderDetails: 15 }
        }
      ]
      
      setOptions(mockOptions)
      setFilteredOptions(mockOptions)
    } catch (error) {
      console.error('Error fetching options:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleOptionSelect = (option: ServiceOption) => {
    onOptionSelect(option)
  }

  const handleCreateOption = () => {
    setIsFormOpen(true)
    setEditingOption(null)
    setFormData({
      name: '',
      description: '',
      price: 0,
      discountRate: 0,
      isActive: true
    })
  }

  const handleEditOption = (option: ServiceOption) => {
    setEditingOption(option)
    setFormData({
      name: option.name,
      description: option.description || '',
      price: option.price || 0,
      discountRate: option.discountRate || 0,
      isActive: option.isActive
    })
    setIsFormOpen(true)
  }

  const handleDeleteOption = async (optionId: string) => {
    if (confirm('Are you sure you want to delete this option?')) {
      try {
        // Mock delete - replace with actual API call
        setOptions(prev => prev.filter(option => option.id !== optionId))
        setFilteredOptions(prev => prev.filter(option => option.id !== optionId))
      } catch (error) {
        console.error('Error deleting option:', error)
      }
    }
  }

  const handleSubmitForm = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      if (editingOption) {
        // Update existing option
        const updatedOption = { ...editingOption, ...formData }
        setOptions(prev => prev.map(option => option.id === editingOption.id ? updatedOption : option))
        setFilteredOptions(prev => prev.map(option => option.id === editingOption.id ? updatedOption : option))
      } else {
        // Create new option
        const newOption: ServiceOption = {
          id: Date.now().toString(),
          serviceId: service.id,
          ...formData,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          service: { id: service.id, name: service.name, category: service.category },
          _count: { features: 0, orderDetails: 0 }
        }
        setOptions(prev => [...prev, newOption])
        setFilteredOptions(prev => [...prev, newOption])
      }
      setIsFormOpen(false)
      setEditingOption(null)
    } catch (error) {
      console.error('Error saving option:', error)
    }
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-12 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Service Options</h2>
          <p className="text-sm text-gray-600">Manage options for {service.name}</p>
        </div>
        <button
          onClick={handleCreateOption}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Add Option
        </button>
      </div>

      {/* Search and Filters */}
      <div className="mb-6 flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search options..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
          />
        </div>
        <button
          onClick={() => {/* Toggle filters */}}
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
        >
          <FunnelIcon className="h-4 w-4 mr-2" />
          Filters
        </button>
      </div>

      {/* Options Content */}
      {viewMode === 'list' && (
        <div>
          {/* List Headers */}
          <div className="bg-gray-50 border border-gray-200 rounded-none px-4 py-3 mb-2">
            <div className="flex items-center">
              <div className="w-6"></div> {/* Space for icon */}
              <div className="flex-1 min-w-0">
                <span className="text-xs font-bold text-gray-700 uppercase tracking-wider">Option</span>
              </div>
              <div className="w-24">
                <span className="text-xs font-bold text-gray-700 uppercase tracking-wider">Price</span>
              </div>
              <div className="w-20">
                <span className="text-xs font-bold text-gray-700 uppercase tracking-wider">Features</span>
              </div>
              <div className="w-20">
                <span className="text-xs font-bold text-gray-700 uppercase tracking-wider">Status</span>
              </div>
              <div className="w-32">
                <span className="text-xs font-bold text-gray-700 uppercase tracking-wider">Actions</span>
              </div>
            </div>
          </div>

          {/* List Items */}
          <div className="space-y-1">
            {filteredOptions.map((option) => (
              <motion.div
                key={option.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className={`flex items-center py-2 px-4 rounded-none cursor-pointer transition-all duration-200 border border-gray-200 ${
                  selectedOption?.id === option.id
                    ? 'bg-orange-50 border-orange-300'
                    : 'bg-white hover:bg-gray-50'
                }`}
                onClick={() => handleOptionSelect(option)}
              >
                {/* Option Icon */}
                <div className="w-6">
                  <i className="fas fa-list-ul text-orange-500 text-base"></i>
                </div>

                {/* Option Name & Description */}
                <div className="flex-1 min-w-0">
                  <h3 className={`font-bold text-sm truncate ${selectedOption?.id === option.id ? 'text-orange-900' : 'text-gray-900'}`}>
                    {option.name}
                  </h3>
                  {option.description && (
                    <p className={`text-xs truncate ${selectedOption?.id === option.id ? 'text-orange-600' : 'text-gray-600'}`}>
                      {option.description}
                    </p>
                  )}
                </div>

                {/* Price */}
                <div className="w-24">
                  {option.price && option.price > 0 ? (
                    <span className="text-xs font-bold text-orange-600">
                      ${option.price.toLocaleString()}
                    </span>
                  ) : (
                    <span className="text-xs font-bold text-gray-400">Free</span>
                  )}
                  {option.discountRate && option.discountRate > 0 && (
                    <p className="text-xs text-red-600 font-medium">
                      {option.discountRate}% off
                    </p>
                  )}
                </div>

                {/* Features Count */}
                <div className="w-20">
                  <span className="inline-flex items-center px-2 py-1 rounded-none text-xs font-medium bg-purple-100 text-purple-800">
                    {option._count?.features || 0}
                  </span>
                </div>

                {/* Status */}
                <div className="w-20">
                  <span className={`inline-flex items-center px-2 py-1 rounded-none text-xs font-medium ${
                    option.isActive
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {option.isActive ? 'Active' : 'Inactive'}
                  </span>
                </div>

                {/* Actions */}
                <div className="flex items-center space-x-2 w-32">
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleEditOption(option)
                    }}
                    className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
                    title="Edit Option"
                  >
                    <PencilIcon className="h-3 w-3" />
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleDeleteOption(option.id)
                    }}
                    className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                    title="Delete Option"
                  >
                    <TrashIcon className="h-3 w-3" />
                  </button>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {viewMode === 'table' && (
        <div className="bg-white rounded-none border border-gray-200 overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                Option
              </th>
              <th className="px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                Price
              </th>
              <th className="px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                Features
              </th>
              <th className="px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                Status
              </th>
              <th className="px-4 py-3 text-right text-xs font-bold text-gray-700 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredOptions.map((option) => (
              <motion.tr
                key={option.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className={`cursor-pointer transition-all duration-200 hover:bg-gray-50 ${
                  selectedOption?.id === option.id ? 'bg-orange-50 border-l-4 border-l-orange-500' : ''
                }`}
                onClick={() => handleOptionSelect(option)}
              >
                <td className="px-4 py-3">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <i className="fas fa-list-ul text-orange-500 text-base"></i>
                    </div>
                    <div className="min-w-0 flex-1">
                      <p className="text-xs font-bold text-gray-900 truncate">{option.name}</p>
                      {option.description && (
                        <p className="text-xs text-gray-500 truncate">{option.description}</p>
                      )}
                    </div>
                  </div>
                </td>
                <td className="px-4 py-3">
                  <div className="flex flex-col">
                    {option.price && option.price > 0 ? (
                      <span className="text-sm font-bold text-orange-600">
                        ${option.price.toLocaleString()}
                      </span>
                    ) : (
                      <span className="text-sm font-bold text-gray-400">Free</span>
                    )}
                    {option.discountRate && option.discountRate > 0 && (
                      <span className="text-xs text-red-600 font-medium">
                        {option.discountRate}% off
                      </span>
                    )}
                  </div>
                </td>
                <td className="px-4 py-3">
                  <span className="inline-flex items-center px-2 py-1 rounded-none text-xs font-medium bg-purple-100 text-purple-800">
                    {option._count?.features || 0} features
                  </span>
                </td>
                <td className="px-4 py-3">
                  <span className={`inline-flex items-center px-2 py-1 rounded-none text-xs font-medium ${
                    option.isActive
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {option.isActive ? 'Active' : 'Inactive'}
                  </span>
                </td>
                <td className="px-4 py-3 text-right">
                  <div className="flex items-center justify-end space-x-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        handleEditOption(option)
                      }}
                      className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
                      title="Edit Option"
                    >
                      <PencilIcon className="h-3 w-3" />
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        handleDeleteOption(option.id)
                      }}
                      className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      title="Delete Option"
                    >
                      <TrashIcon className="h-3 w-3" />
                    </button>
                  </div>
                </td>
              </motion.tr>
            ))}
          </tbody>
        </table>
        </div>
      )}

      {/* Create/Edit Modal */}
      <AnimatePresence>
        {isFormOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            onClick={() => setIsFormOpen(false)}
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              className="bg-white rounded-none p-6 w-full max-w-md mx-4"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {editingOption ? 'Edit Option' : 'Create Option'}
              </h3>
              <form onSubmit={handleSubmitForm} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Name</label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="mt-1 block w-full border border-gray-300 rounded-none px-3 py-2 focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Description</label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    className="mt-1 block w-full border border-gray-300 rounded-none px-3 py-2 focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                    rows={3}
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Price</label>
                    <input
                      type="number"
                      value={formData.price}
                      onChange={(e) => setFormData({ ...formData, price: parseFloat(e.target.value) || 0 })}
                      className="mt-1 block w-full border border-gray-300 rounded-none px-3 py-2 focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                      min="0"
                      step="0.01"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Discount Rate (%)</label>
                    <input
                      type="number"
                      value={formData.discountRate}
                      onChange={(e) => setFormData({ ...formData, discountRate: parseFloat(e.target.value) || 0 })}
                      className="mt-1 block w-full border border-gray-300 rounded-none px-3 py-2 focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                      min="0"
                      max="100"
                      step="0.1"
                    />
                  </div>
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.isActive}
                    onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                    className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded-none"
                  />
                  <label className="ml-2 block text-sm text-gray-900">Active</label>
                </div>
                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => setIsFormOpen(false)}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-none hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 text-sm font-medium text-white bg-orange-600 border border-transparent rounded-none hover:bg-orange-700"
                  >
                    {editingOption ? 'Update' : 'Create'}
                  </button>
                </div>
              </form>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
