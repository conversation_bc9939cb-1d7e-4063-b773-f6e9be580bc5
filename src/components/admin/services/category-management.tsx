'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  FolderIcon,
  FolderOpenIcon,
  ChevronRightIcon,
  ChevronDownIcon,
  EyeIcon,
  EyeSlashIcon
} from '@heroicons/react/24/outline'
import { CategoryHeader } from './category-header'

interface Category {
  id: string
  name: string
  description?: string
  parentId?: string
  isActive: boolean
  displayOrder: number
  children?: Category[]
  _count?: {
    services: number
    children: number
  }
}

interface CategoryManagementProps {
  selectedCategory: Category | null
  onCategorySelect: (category: Category | null) => void
}

interface CategoryFormData {
  name: string
  description: string
  parentId: string
  isActive: boolean
  displayOrder: number
}

export function CategoryManagement({ selectedCategory, onCategorySelect }: CategoryManagementProps) {
  const [categories, setCategories] = useState<Category[]>([])
  const [filteredCategories, setFilteredCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [isFormOpen, setIsFormOpen] = useState(false)
  const [editingCategory, setEditingCategory] = useState<Category | null>(null)
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set())
  const [searchQuery, setSearchQuery] = useState('')
  const [currentFilters, setCurrentFilters] = useState<Record<string, string>>({})
  const [viewMode, setViewMode] = useState<'table' | 'list' | 'grid' | 'card'>('table')
  const [density, setDensity] = useState<'compact' | 'comfortable' | 'spacious'>('comfortable')

  const [formData, setFormData] = useState<CategoryFormData>({
    name: '',
    description: '',
    parentId: '',
    isActive: true,
    displayOrder: 0
  })

  const filters = [
    {
      key: 'status',
      label: 'Status',
      type: 'select' as const,
      options: [
        { value: '', label: 'All Status' },
        { value: 'active', label: 'Active' },
        { value: 'inactive', label: 'Inactive' }
      ]
    },
    {
      key: 'parent',
      label: 'Parent Category',
      type: 'select' as const,
      options: [
        { value: '', label: 'All Categories' },
        { value: 'root', label: 'Root Categories' },
        { value: 'sub', label: 'Sub Categories' }
      ]
    }
  ]

  useEffect(() => {
    fetchCategories()
  }, [])

  useEffect(() => {
    filterAndSortCategories()
  }, [categories, searchQuery, currentFilters])

  const filterAndSortCategories = () => {
    let filtered = [...categories]

    // Apply search filter
    if (searchQuery.trim()) {
      const searchLower = searchQuery.toLowerCase()
      filtered = filtered.filter(category =>
        category.name.toLowerCase().includes(searchLower) ||
        (category.description && category.description.toLowerCase().includes(searchLower))
      )
    }

    // Apply status filter
    if (currentFilters.status) {
      if (currentFilters.status === 'active') {
        filtered = filtered.filter(category => category.isActive)
      } else if (currentFilters.status === 'inactive') {
        filtered = filtered.filter(category => !category.isActive)
      }
    }

    // Apply parent filter
    if (currentFilters.parent) {
      if (currentFilters.parent === 'root') {
        filtered = filtered.filter(category => !category.parentId)
      } else if (currentFilters.parent === 'sub') {
        filtered = filtered.filter(category => category.parentId)
      }
    }

    setFilteredCategories(filtered)
  }

  const buildCategoryTree = (flatCategories: any[]): Category[] => {
    const categoryMap = new Map()
    const rootCategories: Category[] = []

    // Transform and create map
    flatCategories.forEach(cat => {
      const category: Category = {
        id: String(cat.id),
        name: cat.categname || cat.name,
        description: cat.categdesc || cat.description,
        parentId: cat.parentid ? String(cat.parentid) : undefined,
        isActive: cat.isactive,
        displayOrder: cat.displayorder || 0,
        children: [],
        _count: cat._count
      }
      categoryMap.set(category.id, category)
    })

    // Build tree structure
    categoryMap.forEach(category => {
      if (category.parentId && categoryMap.has(category.parentId)) {
        categoryMap.get(category.parentId).children.push(category)
      } else {
        rootCategories.push(category)
      }
    })

    // Sort by display order
    const sortCategories = (cats: Category[]) => {
      cats.sort((a, b) => a.displayOrder - b.displayOrder)
      cats.forEach(cat => {
        if (cat.children) {
          sortCategories(cat.children)
        }
      })
    }

    sortCategories(rootCategories)
    return rootCategories
  }

  const fetchCategories = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/categories?limit=100')

      if (response.ok) {
        const data = await response.json()
        const categoriesData = data.data || data.categories || []
        setCategories(buildCategoryTree(categoriesData))
      } else {
        console.error('Failed to fetch categories:', response.status, response.statusText)
        setCategories([])
      }
    } catch (error) {
      console.error('Error fetching categories:', error)
      setCategories([])
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = (category: Category) => {
    setEditingCategory(category)
    setFormData({
      name: category.name,
      description: category.description || '',
      parentId: category.parentId || '',
      isActive: category.isActive,
      displayOrder: category.displayOrder
    })
    setIsFormOpen(true)
  }

  const handleDelete = async (category: Category) => {
    if (!confirm(`Are you sure you want to delete "${category.name}"?`)) return

    try {
      const response = await fetch(`/api/admin/categories/${category.id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        await fetchCategories()
        if (selectedCategory?.id === category.id) {
          onCategorySelect(null)
        }
      } else {
        const errorData = await response.json()
        alert(errorData.message || 'Failed to delete category')
      }
    } catch (error) {
      console.error('Error deleting category:', error)
      alert('An error occurred while deleting the category')
    }
  }

  const handleToggleActive = async (category: Category) => {
    try {
      const response = await fetch(`/api/admin/categories/${category.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          categname: category.name,
          categdesc: category.description,
          parentid: category.parentId ? Number(category.parentId) : 0,
          isactive: !category.isActive,
          displayorder: category.displayOrder
        })
      })
      if (response.ok) {
        fetchCategories()
      }
    } catch (error) {
      console.error('Error toggling category status:', error)
    }
  }

  const toggleExpanded = (categoryId: string) => {
    const newExpanded = new Set(expandedCategories)
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId)
    } else {
      newExpanded.add(categoryId)
    }
    setExpandedCategories(newExpanded)
  }

  const renderCategory = (category: Category, level: number = 0) => {
    const isExpanded = expandedCategories.has(category.id)
    const hasChildren = category.children && category.children.length > 0
    const isSelected = selectedCategory?.id === category.id

    return (
      <div key={category.id} className="select-none">
        <div
          className={`flex items-center space-x-2 p-3 rounded-none cursor-pointer transition-colors ${
            isSelected
              ? 'bg-blue-50 border border-blue-200'
              : 'hover:bg-gray-100'
          }`}
          style={{ marginLeft: `${level * 20}px` }}
          onClick={() => onCategorySelect(category)}
        >
          {hasChildren ? (
            <button
              onClick={(e) => {
                e.stopPropagation()
                toggleExpanded(category.id)
              }}
              className="p-1 hover:bg-gray-200 rounded"
            >
              {isExpanded ? (
                <ChevronDownIcon className="h-4 w-4 text-gray-500" />
              ) : (
                <ChevronRightIcon className="h-4 w-4 text-gray-500" />
              )}
            </button>
          ) : (
            <div className="w-6" />
          )}

          <div className="flex items-center space-x-2 flex-1">
            {hasChildren ? (
              isExpanded ? (
                <FolderOpenIcon className="h-5 w-5 text-blue-500" />
              ) : (
                <FolderIcon className="h-5 w-5 text-blue-500" />
              )
            ) : (
              <FolderIcon className="h-5 w-5 text-gray-400" />
            )}

            <div className="flex-1">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className={`font-medium text-base ${isSelected ? 'text-blue-900' : 'text-gray-900'}`}>
                    {category.name}
                  </h3>
                  {category.description && (
                    <p className={`text-sm ${isSelected ? 'text-blue-600' : 'text-gray-500'}`}>
                      {category.description}
                    </p>
                  )}
                </div>
                
                <div className="flex items-center space-x-1">
                  {category._count && typeof category._count.services === 'number' && (
                    <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs">
                      {category._count.services} services
                    </span>
                  )}

                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleEdit(category)
                    }}
                    className="text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded p-1"
                    title="Edit category"
                  >
                    <PencilIcon className="h-4 w-4" />
                  </button>

                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleToggleActive(category)
                    }}
                    className={`rounded transition-colors p-1 ${
                      category.isActive
                        ? 'text-green-600 hover:text-green-700 hover:bg-green-50'
                        : 'text-gray-400 hover:text-gray-600 hover:bg-gray-50'
                    }`}
                    title={category.isActive ? 'Deactivate category' : 'Activate category'}
                  >
                    {category.isActive ? (
                      <EyeIcon className="h-4 w-4" />
                    ) : (
                      <EyeSlashIcon className="h-4 w-4" />
                    )}
                  </button>

                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleDelete(category)
                    }}
                    className="text-gray-400 hover:text-red-600 hover:bg-red-50 rounded p-1"
                    title="Delete category"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {hasChildren && isExpanded && (
          <div className="ml-6">
            {category.children?.map((child) => renderCategory(child, level + 1))}
          </div>
        )}
      </div>
    )
  }

  const handleCreateClick = () => {
    setIsFormOpen(true)
    setEditingCategory(null)
    setFormData({
      name: '',
      description: '',
      parentId: '',
      isActive: true,
      displayOrder: 0
    })
  }

  const handleFiltersChange = (newFilters: Record<string, string>) => {
    setCurrentFilters(newFilters)
  }

  const renderCategoryCard = (category: Category, isLargeCard: boolean = false) => {
    const isSelected = selectedCategory?.id === category.id
    const hasChildren = category.children && category.children.length > 0

    return (
      <div
        key={category.id}
        className={`bg-gray-50 border rounded-none cursor-pointer transition-all duration-200 ${
          isSelected
            ? 'border-blue-500 shadow-md bg-blue-50'
            : 'border-gray-200 hover:border-gray-300 hover:shadow-sm hover:bg-gray-100'
        } ${isLargeCard ? 'p-6' : 'p-4'}`}
        onClick={() => onCategorySelect(category)}
      >
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              {hasChildren ? (
                <FolderIcon className="h-5 w-5 text-blue-500" />
              ) : (
                <FolderIcon className="h-5 w-5 text-gray-400" />
              )}
              <h3 className={`font-semibold ${isSelected ? 'text-blue-900' : 'text-gray-900'}`}>
                {category.name}
              </h3>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                category.isActive 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-gray-100 text-gray-600'
              }`}>
                {category.isActive ? 'Active' : 'Inactive'}
              </span>
            </div>
            
            {category.description && (
              <p className={`text-sm mb-3 ${isSelected ? 'text-blue-600' : 'text-gray-600'}`}>
                {category.description}
              </p>
            )}

            <div className="flex items-center space-x-4 text-xs text-gray-500">
              {category._count && typeof category._count.services === 'number' && (
                <span>{category._count.services} services</span>
              )}
              {hasChildren && (
                <span>{category.children?.length} subcategories</span>
              )}
              <span>Order: {category.displayOrder}</span>
            </div>
          </div>

          <div className="flex items-center space-x-1 ml-4">
            <button
              onClick={(e) => {
                e.stopPropagation()
                handleEdit(category)
              }}
              className="text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded p-1"
              title="Edit category"
            >
              <PencilIcon className="h-4 w-4" />
            </button>

            <button
              onClick={(e) => {
                e.stopPropagation()
                handleToggleActive(category)
              }}
              className={`rounded transition-colors p-1 ${
                category.isActive
                  ? 'text-green-600 hover:text-green-700 hover:bg-green-50'
                  : 'text-gray-400 hover:text-gray-600 hover:bg-gray-50'
              }`}
              title={category.isActive ? 'Deactivate category' : 'Activate category'}
            >
              {category.isActive ? (
                <EyeIcon className="h-4 w-4" />
              ) : (
                <EyeSlashIcon className="h-4 w-4" />
              )}
            </button>

            <button
              onClick={(e) => {
                e.stopPropagation()
                handleDelete(category)
              }}
              className="text-gray-400 hover:text-red-600 hover:bg-red-50 rounded p-1"
              title="Delete category"
            >
              <TrashIcon className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    )
  }

  const getAllCategories = (cats: Category[]): Category[] => {
    let all: Category[] = []
    cats.forEach(cat => {
      all.push(cat)
      if (cat.children && cat.children.length > 0) {
        all = all.concat(getAllCategories(cat.children))
      }
    })
    return all
  }

  return (
    <div className="space-y-6">
      <CategoryHeader
        title="Categories"
        description="Manage service categories and subcategories"
        searchPlaceholder="Search categories by name or description..."
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        enableSearch={true}
        enableFilters={true}
        enableViewControls={true}
        enableCreate={true}
        onCreateClick={handleCreateClick}
        createButtonText="Add Category"
        viewMode={viewMode}
        onViewModeChange={setViewMode}
        filters={filters}
        onFiltersChange={handleFiltersChange}
        currentFilters={currentFilters}
        itemCount={filteredCategories.length}
        totalItems={categories.length}
      />

      {/* Categories List */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {loading ? (
          <div className="p-6 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading categories...</p>
          </div>
        ) : filteredCategories.length === 0 ? (
          <div className="p-6 text-center">
            <FolderIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No categories found</h3>
            <p className="text-gray-600 mb-4">
              {searchQuery || Object.keys(currentFilters).some(key => currentFilters[key]) 
                ? 'Try adjusting your search terms or filters.' 
                : 'Get started by creating your first category.'}
            </p>
            <button
              onClick={handleCreateClick}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Add Category
            </button>
          </div>
        ) : (
          <div className="p-4">
            {viewMode === 'table' && (
              <div className="bg-white rounded-none border border-gray-200 overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                        Category
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                        Parent
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                        Services
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-4 py-3 text-right text-xs font-bold text-gray-700 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {getAllCategories(filteredCategories).map((category) => (
                      <motion.tr
                        key={category.id}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className={`cursor-pointer transition-all duration-200 hover:bg-gray-50 ${
                          selectedCategory?.id === category.id ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
                        }`}
                        onClick={() => onCategorySelect(category)}
                      >
                        <td className="px-4 py-3">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              <FolderIcon className="h-5 w-5 text-blue-500" />
                            </div>
                            <div className="min-w-0 flex-1">
                              <p className="text-sm font-bold text-gray-900 truncate">{category.name}</p>
                              {category.description && (
                                <p className="text-xs text-gray-500 truncate">{category.description}</p>
                              )}
                            </div>
                          </div>
                        </td>
                        <td className="px-4 py-3">
                          {category.parentId ? (
                            <span className="text-sm text-gray-600">
                              {categories.find(c => c.id === category.parentId)?.name || 'Unknown'}
                            </span>
                          ) : (
                            <span className="text-sm text-gray-400 italic">Root Category</span>
                          )}
                        </td>
                        <td className="px-4 py-3">
                          <span className="inline-flex items-center px-2 py-1 rounded-none text-xs font-medium bg-blue-100 text-blue-800">
                            {category._count?.services || 0} services
                          </span>
                        </td>
                        <td className="px-4 py-3">
                          <span className={`inline-flex items-center px-2 py-1 rounded-none text-xs font-medium ${
                            category.isActive
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {category.isActive ? 'Active' : 'Inactive'}
                          </span>
                        </td>
                        <td className="px-4 py-3 text-right">
                          <div className="flex items-center justify-end space-x-2">
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                handleEdit(category)
                              }}
                              className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                              title="Edit Category"
                            >
                              <PencilIcon className="h-3 w-3" />
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                handleToggleActive(category)
                              }}
                              className={`inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white ${
                                category.isActive
                                  ? 'bg-orange-600 hover:bg-orange-700 focus:ring-orange-500'
                                  : 'bg-green-600 hover:bg-green-700 focus:ring-green-500'
                              } focus:outline-none focus:ring-2 focus:ring-offset-2`}
                              title={category.isActive ? 'Deactivate Category' : 'Activate Category'}
                            >
                              {category.isActive ? (
                                <EyeSlashIcon className="h-3 w-3" />
                              ) : (
                                <EyeIcon className="h-3 w-3" />
                              )}
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                handleDelete(category)
                              }}
                              className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                              title="Delete Category"
                            >
                              <TrashIcon className="h-3 w-3" />
                            </button>
                          </div>
                        </td>
                      </motion.tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}

            {viewMode === 'list' && (
              <div className="space-y-2">
                {filteredCategories.map((category) => renderCategory(category))}
              </div>
            )}

            {viewMode === 'grid' && (
              <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                {getAllCategories(filteredCategories).map((category) => renderCategoryCard(category, false))}
              </div>
            )}

            {viewMode === 'card' && (
              <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
                {getAllCategories(filteredCategories).map((category) => renderCategoryCard(category, true))}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Form Modal */}
      <AnimatePresence>
        {isFormOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            onClick={() => setIsFormOpen(false)}
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              className="bg-white rounded-lg p-6 w-full max-w-md mx-4"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-lg font-semibold mb-4">
                {editingCategory ? 'Edit Category' : 'Add Category'}
              </h3>
              
              <form onSubmit={async (e) => {
                e.preventDefault()
                
                try {
                  const url = editingCategory 
                    ? `/api/admin/categories/${editingCategory.id}`
                    : '/api/admin/categories'
                  
                  const method = editingCategory ? 'PUT' : 'POST'
                  
                  const response = await fetch(url, {
                    method,
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                      categname: formData.name,
                      categdesc: formData.description,
                      parentid: formData.parentId ? Number(formData.parentId) : 0,
                      isactive: formData.isActive,
                      displayorder: formData.displayOrder
                    })
                  })

                  if (response.ok) {
                    setIsFormOpen(false)
                    fetchCategories()
                  } else {
                    const errorData = await response.json()
                    alert(errorData.message || 'Failed to save category')
                  }
                } catch (error) {
                  console.error('Error saving category:', error)
                  alert('An error occurred while saving the category')
                }
              }}>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Name *
                    </label>
                    <input
                      type="text"
                      required
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Description
                    </label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Parent Category
                    </label>
                    <select
                      value={formData.parentId}
                      onChange={(e) => setFormData({ ...formData, parentId: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">No parent (root category)</option>
                      {categories.map((cat) => (
                        <option key={cat.id} value={cat.id}>
                          {cat.name}
                        </option>
                      ))}
                    </select>
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.isActive}
                        onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">Active</span>
                    </label>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Display Order
                    </label>
                    <input
                      type="number"
                      value={formData.displayOrder}
                      onChange={(e) => setFormData({ ...formData, displayOrder: Number(e.target.value) })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>
                
                <div className="flex justify-end space-x-3 mt-6">
                  <button
                    type="button"
                    onClick={() => setIsFormOpen(false)}
                    className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    {editingCategory ? 'Update' : 'Create'}
                  </button>
                </div>
              </form>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
