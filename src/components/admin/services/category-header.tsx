'use client';

import React, { useState, useRef, useEffect } from 'react';
import {
  MagnifyingGlassIcon,
  XMarkIcon,
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon,
  RectangleStackIcon,
  PlusIcon,
  ChevronDownIcon,
} from '@heroicons/react/24/outline';
import { motion, AnimatePresence } from 'framer-motion';

interface CategoryHeaderProps {
  title: string;
  description?: string;
  searchPlaceholder?: string;
  searchQuery: string;
  onSearchChange: (query: string) => void;
  enableSearch?: boolean;
  enableFilters?: boolean;
  enableViewControls?: boolean;
  enableCreate?: boolean;
  onCreateClick?: () => void;
  createButtonText?: string;
  viewMode?: 'list' | 'grid' | 'card';
  onViewModeChange?: (mode: 'list' | 'grid' | 'card') => void;
  filters?: Array<{
    key: string;
    label: string;
    type: 'select' | 'text' | 'date';
    options?: Array<{ value: string; label: string }>;
  }>;
  onFiltersChange?: (filters: Record<string, string>) => void;
  currentFilters?: Record<string, string>;
  itemCount?: number;
  totalItems?: number;
}

export function CategoryHeader({
  title,
  description,
  searchPlaceholder = "Search...",
  searchQuery,
  onSearchChange,
  enableSearch = true,
  enableFilters = true,
  enableViewControls = true,
  enableCreate = true,
  onCreateClick,
  createButtonText = "Add Item",
  viewMode = 'list',
  onViewModeChange,
  filters = [],
  onFiltersChange,
  currentFilters = {},
  itemCount = 0,
  totalItems = 0,
}: CategoryHeaderProps) {
  const [showFilters, setShowFilters] = useState(false);
  const [localFilters, setLocalFilters] = useState<Record<string, string>>(currentFilters);
  const filtersRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setLocalFilters(currentFilters);
  }, [currentFilters]);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (filtersRef.current && !filtersRef.current.contains(event.target as Node)) {
        setShowFilters(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleFilterChange = (key: string, value: string) => {
    const newFilters = { ...localFilters };
    if (value) {
      newFilters[key] = value;
    } else {
      delete newFilters[key];
    }
    setLocalFilters(newFilters);
    onFiltersChange?.(newFilters);
  };

  const clearAllFilters = () => {
    setLocalFilters({});
    onFiltersChange?.({});
  };

  const activeFiltersCount = Object.keys(localFilters).filter(key => localFilters[key]).length;

  const viewModeIcons = {
    list: ListBulletIcon,
    grid: Squares2X2Icon,
    card: RectangleStackIcon,
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200/50 overflow-hidden">
      {/* Header with gradient background */}
      <div className="relative bg-gradient-to-r from-gray-50 to-white border-b border-gray-200/50 p-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-1.5 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-md shadow-sm">
              <FunnelIcon className="h-4 w-4 text-white" />
            </div>
            <div>
              <h2 className="text-lg font-bold text-gray-900">{title}</h2>
              {description && (
                <p className="text-sm font-medium text-gray-600">{description}</p>
              )}
            </div>
          </div>

          {enableCreate && onCreateClick && (
            <button
              onClick={onCreateClick}
              className="group inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white text-sm font-medium rounded-lg hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 shadow-sm hover:shadow-md"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              {createButtonText}
            </button>
          )}
        </div>
      </div>

      {/* Controls Row */}
      <div className="p-3 bg-white">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4 flex-1">
            {/* Search */}
            {enableSearch && (
              <div className="relative flex-1 max-w-md">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-4 w-4 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder={searchPlaceholder}
                  value={searchQuery}
                  onChange={(e) => onSearchChange(e.target.value)}
                  className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm font-medium bg-gray-50 focus:bg-white transition-all duration-200 placeholder-gray-500"
                />
                {searchQuery && (
                  <button
                    onClick={() => onSearchChange('')}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center group"
                  >
                    <XMarkIcon className="h-4 w-4 text-gray-400 group-hover:text-gray-600 transition-colors" />
                  </button>
                )}
              </div>
            )}

            {/* Filters */}
            {enableFilters && filters.length > 0 && (
              <div className="relative" ref={filtersRef}>
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className={`group inline-flex items-center px-3 py-2 border rounded-lg text-sm font-medium transition-all duration-200 ${
                    activeFiltersCount > 0
                      ? 'border-blue-300 bg-blue-50 text-blue-700 hover:bg-blue-100 shadow-sm'
                      : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50 hover:border-gray-400'
                  }`}
                >
                  <FunnelIcon className="h-4 w-4 mr-2" />
                  Filters
                  {activeFiltersCount > 0 && (
                    <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-semibold bg-blue-100 text-blue-800">
                      {activeFiltersCount}
                    </span>
                  )}
                  <ChevronDownIcon className={`h-4 w-4 ml-2 transition-transform duration-200 ${showFilters ? 'rotate-180' : ''}`} />
                </button>

                <AnimatePresence>
                  {showFilters && (
                    <motion.div
                      initial={{ opacity: 0, y: -10, scale: 0.95 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      exit={{ opacity: 0, y: -10, scale: 0.95 }}
                      transition={{ duration: 0.2, ease: [0.4, 0.0, 0.2, 1] }}
                      className="absolute top-full left-0 mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-20 overflow-hidden"
                    >
                      <div className="p-4">
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center space-x-2">
                            <div className="p-1 bg-blue-100 rounded-md">
                              <FunnelIcon className="h-3 w-3 text-blue-600" />
                            </div>
                            <h3 className="text-xs font-semibold text-gray-400 uppercase tracking-wide">Filters</h3>
                          </div>
                          {activeFiltersCount > 0 && (
                            <button
                              onClick={clearAllFilters}
                              className="text-xs font-semibold text-blue-600 hover:text-blue-700 px-2 py-1 rounded hover:bg-blue-50 transition-colors uppercase tracking-wide"
                            >
                              Clear all
                            </button>
                          )}
                        </div>

                        <div className="space-y-3">
                          {filters.map((filter) => (
                            <div key={filter.key}>
                              <label className="block text-xs font-semibold text-gray-400 uppercase tracking-wide mb-2">
                                {filter.label}
                              </label>
                              {filter.type === 'select' ? (
                                <select
                                  value={localFilters[filter.key] || ''}
                                  onChange={(e) => handleFilterChange(filter.key, e.target.value)}
                                  className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm font-medium bg-gray-50 focus:bg-white transition-all duration-200"
                                >
                                  {filter.options?.map((option) => (
                                    <option key={option.value} value={option.value}>
                                      {option.label}
                                    </option>
                                  ))}
                                </select>
                              ) : filter.type === 'text' ? (
                                <input
                                  type="text"
                                  value={localFilters[filter.key] || ''}
                                  onChange={(e) => handleFilterChange(filter.key, e.target.value)}
                                  className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm font-medium bg-gray-50 focus:bg-white transition-all duration-200"
                                />
                              ) : (
                                <input
                                  type="date"
                                  value={localFilters[filter.key] || ''}
                                  onChange={(e) => handleFilterChange(filter.key, e.target.value)}
                                  className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm font-medium bg-gray-50 focus:bg-white transition-all duration-200"
                                />
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            )}

            {/* View Controls */}
            {enableViewControls && onViewModeChange && (
              <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
                {(['list', 'grid', 'card'] as const).map((mode) => {
                  const Icon = viewModeIcons[mode];
                  return (
                    <button
                      key={mode}
                      onClick={() => onViewModeChange(mode)}
                      className={`p-2 rounded-md font-medium transition-all duration-200 ${
                        viewMode === mode
                          ? 'bg-white text-blue-600 shadow-sm'
                          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-200'
                      }`}
                      title={`${mode.charAt(0).toUpperCase() + mode.slice(1)} view`}
                    >
                      <Icon className="h-4 w-4" />
                    </button>
                  );
                })}
              </div>
            )}
          </div>

          {/* Item Count */}
          <div className="text-xs font-semibold text-gray-400 uppercase tracking-wide bg-gray-50 px-3 py-1.5 rounded-lg border border-gray-200">
            {itemCount !== totalItems ? (
              <>
                Showing <span className="font-medium text-gray-700">{itemCount}</span> of{' '}
                <span className="font-medium text-gray-700">{totalItems}</span> items
              </>
            ) : (
              <>
                <span className="font-medium text-gray-700">{totalItems}</span> items
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
