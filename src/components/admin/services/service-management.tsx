'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  CogIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  EyeIcon,
  EyeSlashIcon
} from '@heroicons/react/24/outline'

interface Category {
  id: string
  name: string
  description?: string
  parentId?: string
  isActive: boolean
  displayOrder: number
  children?: Category[]
  _count?: {
    services: number
    children: number
  }
}

interface Service {
  id: string
  categoryId: string
  name: string
  description: string
  iconClass?: string
  price: number
  discountRate?: number
  totalDiscount?: number
  manager?: string
  isActive: boolean
  displayOrder: number
  createdAt: string
  updatedAt: string
  category?: {
    id: string
    name: string
  }
  _count?: {
    serviceOptions: number
    orderDetails: number
  }
}

interface ServiceManagementProps {
  category: Category
  selectedService: Service | null
  onServiceSelect: (service: Service | null) => void
}

interface ServiceFormData {
  name: string
  description: string
  price: number
  discountRate: number
  manager: string
  isActive: boolean
  displayOrder: number
}

export function ServiceManagement({ category, selectedService, onServiceSelect }: ServiceManagementProps) {
  const [services, setServices] = useState<Service[]>([])
  const [filteredServices, setFilteredServices] = useState<Service[]>([])
  const [loading, setLoading] = useState(true)
  const [isFormOpen, setIsFormOpen] = useState(false)
  const [editingService, setEditingService] = useState<Service | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('')
  const [viewMode, setViewMode] = useState<'table' | 'list'>('list')

  // Function to get service-specific icons based on service name/type
  const getServiceIcon = (service: Service): string => {
    const name = service.name.toLowerCase()

    // Web Development - More specific icons
    if (name.includes('website') || name.includes('web development')) {
      return 'fa-code text-blue-500'
    }
    if (name.includes('web design') || name.includes('frontend')) {
      return 'fa-paint-brush text-purple-500'
    }
    if (name.includes('backend') || name.includes('api')) {
      return 'fa-database text-gray-600'
    }

    // E-commerce
    if (name.includes('ecommerce') || name.includes('online store') || name.includes('shop')) {
      return 'fa-store text-orange-500'
    }
    if (name.includes('payment') || name.includes('checkout')) {
      return 'fa-credit-card text-green-600'
    }

    // Mobile Development
    if (name.includes('mobile app') || name.includes('ios') || name.includes('android')) {
      return 'fa-mobile-alt text-green-500'
    }
    if (name.includes('react native') || name.includes('flutter')) {
      return 'fa-mobile text-blue-400'
    }

    // Design Services
    if (name.includes('logo') || name.includes('branding')) {
      return 'fa-copyright text-pink-500'
    }
    if (name.includes('ui design') || name.includes('interface')) {
      return 'fa-desktop text-purple-400'
    }
    if (name.includes('graphic design') || name.includes('print')) {
      return 'fa-image text-red-400'
    }

    // Marketing & SEO
    if (name.includes('seo') || name.includes('search engine')) {
      return 'fa-search text-green-600'
    }
    if (name.includes('social media') || name.includes('facebook') || name.includes('instagram')) {
      return 'fa-share-alt text-blue-600'
    }
    if (name.includes('email marketing') || name.includes('newsletter')) {
      return 'fa-envelope text-red-500'
    }
    if (name.includes('advertising') || name.includes('ads')) {
      return 'fa-bullhorn text-orange-600'
    }

    // Technical Services
    if (name.includes('hosting') || name.includes('server')) {
      return 'fa-server text-gray-500'
    }
    if (name.includes('domain') || name.includes('dns')) {
      return 'fa-globe text-blue-300'
    }
    if (name.includes('ssl') || name.includes('certificate')) {
      return 'fa-lock text-green-700'
    }
    if (name.includes('backup') || name.includes('restore')) {
      return 'fa-cloud text-gray-400'
    }

    // Maintenance & Support
    if (name.includes('maintenance') || name.includes('update')) {
      return 'fa-wrench text-yellow-500'
    }
    if (name.includes('support') || name.includes('help')) {
      return 'fa-headset text-teal-500'
    }
    if (name.includes('monitoring') || name.includes('uptime')) {
      return 'fa-heartbeat text-red-600'
    }

    // Analytics & Reporting
    if (name.includes('analytics') || name.includes('google analytics')) {
      return 'fa-chart-line text-teal-600'
    }
    if (name.includes('report') || name.includes('dashboard')) {
      return 'fa-chart-bar text-indigo-500'
    }

    // Consulting & Strategy
    if (name.includes('consulting') || name.includes('consultation')) {
      return 'fa-user-tie text-indigo-600'
    }
    if (name.includes('strategy') || name.includes('planning')) {
      return 'fa-chess text-purple-600'
    }
    if (name.includes('audit') || name.includes('review')) {
      return 'fa-clipboard-check text-orange-400'
    }

    // Content Services
    if (name.includes('content') || name.includes('copywriting')) {
      return 'fa-pen text-blue-700'
    }
    if (name.includes('blog') || name.includes('article')) {
      return 'fa-newspaper text-gray-700'
    }

    // Security Services
    if (name.includes('security') || name.includes('penetration')) {
      return 'fa-shield-alt text-red-500'
    }
    if (name.includes('firewall') || name.includes('protection')) {
      return 'fa-shield text-red-700'
    }

    // Default fallback
    return 'fa-cog text-gray-500'
  }

  const [formData, setFormData] = useState<ServiceFormData>({
    name: '',
    description: '',
    price: 0,
    discountRate: 0,
    manager: '',
    isActive: true,
    displayOrder: 0
  })

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm)
    }, 300)
    return () => clearTimeout(timer)
  }, [searchTerm])

  useEffect(() => {
    fetchServices()
  }, [category.id])

  // Fetch services for the selected category
  const fetchServices = async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/admin/services?categoryId=${category.id}&limit=100`)
      
      if (response.ok) {
        const data = await response.json()
        const servicesData = data.data || data.services || []
        setServices(servicesData)
        setFilteredServices(servicesData)
      } else {
        console.error('Failed to fetch services:', response.status, response.statusText)
        setServices([])
        setFilteredServices([])
      }
    } catch (error) {
      console.error('Error fetching services:', error)
      setServices([])
      setFilteredServices([])
    } finally {
      setLoading(false)
    }
  }

  const handleServiceSelect = (service: Service) => {
    onServiceSelect(service)
  }

  const handleCreateService = () => {
    setIsFormOpen(true)
    setEditingService(null)
    setFormData({
      name: '',
      description: '',
      price: 0,
      discountRate: 0,
      manager: '',
      isActive: true,
      displayOrder: 0
    })
  }

  const handleEditService = (service: Service) => {
    setEditingService(service)
    setFormData({
      name: service.name,
      description: service.description,
      price: service.price,
      discountRate: service.discountRate || 0,
      manager: service.manager || '',
      isActive: service.isActive,
      displayOrder: service.displayOrder
    })
    setIsFormOpen(true)
  }

  const handleDeleteService = async (serviceId: string) => {
    if (confirm('Are you sure you want to delete this service?')) {
      try {
        const response = await fetch(`/api/admin/services/${serviceId}`, {
          method: 'DELETE',
        })

        if (response.ok) {
          await fetchServices() // Refresh the services list
        } else {
          const errorData = await response.json()
          alert(errorData.message || 'Failed to delete service')
        }
      } catch (error) {
        console.error('Error deleting service:', error)
        alert('An error occurred while deleting the service')
      }
    }
  }

  const handleSubmitForm = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const serviceData = {
        servicename: formData.name,
        servicedesc: formData.description,
        categoryid: category.id,
        price: formData.price,
        discountrate: formData.discountRate,
        totaldiscount: formData.discountRate > 0 ? (formData.price * formData.discountRate) / 100 : 0,
        manager: formData.manager,
        isactive: formData.isActive,
        displayorder: formData.displayOrder,
        iconclass: ''
      }

      const url = editingService 
        ? `/api/admin/services/${editingService.id}`
        : '/api/admin/services'
      
      const method = editingService ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(serviceData)
      })

      if (response.ok) {
        setIsFormOpen(false)
        setEditingService(null)
        await fetchServices() // Refresh the services list
      } else {
        const errorData = await response.json()
        alert(errorData.message || 'Failed to save service')
      }
    } catch (error) {
      console.error('Error saving service:', error)
      alert('An error occurred while saving the service')
    }
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-12 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Services</h2>
          <p className="text-sm text-gray-600">Manage services under {category.name}</p>
        </div>
        <button
          onClick={handleCreateService}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Add Service
        </button>
      </div>

      {/* Search and Filters */}
      <div className="mb-6 flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search services..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
          />
        </div>
        <button
          onClick={() => {/* Toggle filters */}}
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
        >
          <FunnelIcon className="h-4 w-4 mr-2" />
          Filters
        </button>
      </div>

      {/* Services Content */}
      {viewMode === 'list' && (
        <div>
          {/* List Headers */}
          <div className="bg-gray-50 border border-gray-200 rounded-none px-4 py-3 mb-2">
            <div className="flex items-center">
              <div className="w-6"></div> {/* Space for icon */}
              <div className="flex-1 min-w-0">
                <span className="text-xs font-bold text-gray-700 uppercase tracking-wider">Service</span>
              </div>
              <div className="w-24">
                <span className="text-xs font-bold text-gray-700 uppercase tracking-wider">Price</span>
              </div>
              <div className="w-20">
                <span className="text-xs font-bold text-gray-700 uppercase tracking-wider">Options</span>
              </div>
              <div className="w-20">
                <span className="text-xs font-bold text-gray-700 uppercase tracking-wider">Status</span>
              </div>
              <div className="w-32">
                <span className="text-xs font-bold text-gray-700 uppercase tracking-wider">Actions</span>
              </div>
            </div>
          </div>

          {/* List Items */}
          <div className="space-y-1">
            {filteredServices.map((service) => (
              <motion.div
                key={service.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className={`flex items-center py-2 px-4 rounded-none cursor-pointer transition-all duration-200 border border-gray-200 ${
                  selectedService?.id === service.id
                    ? 'bg-green-50 border-green-300'
                    : 'bg-white hover:bg-gray-50'
                }`}
                onClick={() => handleServiceSelect(service)}
              >
                {/* Service Icon */}
                <div className="w-6">
                  <i className={`fas ${getServiceIcon(service)} text-base`}></i>
                </div>

                {/* Service Name & Description */}
                <div className="flex-1 min-w-0">
                  <h3 className={`font-bold text-base truncate ${selectedService?.id === service.id ? 'text-green-900' : 'text-gray-900'}`}>
                    {service.name}
                  </h3>
                  <p className={`text-sm truncate ${selectedService?.id === service.id ? 'text-green-600' : 'text-gray-600'}`}>
                    {service.description}
                  </p>
                </div>

                {/* Price */}
                <div className="w-24">
                  <span className="text-xs font-bold text-green-600">
                    ${service.price.toLocaleString()}
                  </span>
                  {service.discountRate && service.discountRate > 0 && (
                    <p className="text-xs text-red-600 font-medium">
                      {service.discountRate}% off
                    </p>
                  )}
                </div>

                {/* Options Count */}
                <div className="w-20">
                  <span className="inline-flex items-center px-2 py-1 rounded-none text-xs font-medium bg-blue-100 text-blue-800">
                    {service._count?.serviceOptions || 0}
                  </span>
                </div>

                {/* Status */}
                <div className="w-20">
                  <span className={`inline-flex items-center px-2 py-1 rounded-none text-xs font-medium ${
                    service.isActive
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {service.isActive ? 'Active' : 'Inactive'}
                  </span>
                </div>

                {/* Actions */}
                <div className="flex items-center space-x-2 w-32">
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleEditService(service)
                    }}
                    className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    title="Edit Service"
                  >
                    <PencilIcon className="h-3 w-3" />
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleDeleteService(service.id)
                    }}
                    className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                    title="Delete Service"
                  >
                    <TrashIcon className="h-3 w-3" />
                  </button>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {viewMode === 'table' && (
        <div className="bg-white rounded-none border border-gray-200 overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                Service
              </th>
              <th className="px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                Price
              </th>
              <th className="px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                Options
              </th>
              <th className="px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                Status
              </th>
              <th className="px-4 py-3 text-right text-xs font-bold text-gray-700 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredServices.map((service) => (
              <motion.tr
                key={service.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className={`cursor-pointer transition-all duration-200 hover:bg-gray-50 ${
                  selectedService?.id === service.id ? 'bg-green-50 border-l-4 border-l-green-500' : ''
                }`}
                onClick={() => handleServiceSelect(service)}
              >
                <td className="px-4 py-0.5">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <i className={`fas ${getServiceIcon(service)} text-base`}></i>
                    </div>
                    <div className="min-w-0 flex-1">
                      <p className="text-sm font-bold text-gray-900 truncate">{service.name}</p>
                      <p className="text-sm text-gray-500 truncate">{service.description}</p>
                    </div>
                  </div>
                </td>
                <td className="px-4 py-0.5">
                  <div className="flex flex-col">
                    <span className="text-sm font-bold text-green-600">
                      ${service.price.toLocaleString()}
                    </span>
                    {service.discountRate && service.discountRate > 0 && (
                      <span className="text-xs text-red-600 font-medium">
                        {service.discountRate}% off
                      </span>
                    )}
                  </div>
                </td>
                <td className="px-4 py-0.5">
                  <span className="inline-flex items-center px-2 py-0.5 rounded-none text-xs font-medium bg-blue-100 text-blue-800">
                    {service._count?.serviceOptions || 0} options
                  </span>
                </td>
                <td className="px-4 py-0.5">
                  <span className={`inline-flex items-center px-2 py-0.5 rounded-none text-xs font-medium ${
                    service.isActive
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {service.isActive ? 'Active' : 'Inactive'}
                  </span>
                </td>
                <td className="px-4 py-0.5 text-right">
                  <div className="flex items-center justify-end space-x-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        handleEditService(service)
                      }}
                      className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      title="Edit Service"
                    >
                      <PencilIcon className="h-3 w-3" />
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        handleDeleteService(service.id)
                      }}
                      className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      title="Delete Service"
                    >
                      <TrashIcon className="h-3 w-3" />
                    </button>
                  </div>
                </td>
              </motion.tr>
            ))}
          </tbody>
        </table>
        </div>
      )}

      {/* Create/Edit Modal */}
      <AnimatePresence>
        {isFormOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            onClick={() => setIsFormOpen(false)}
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              className="bg-white rounded-none p-6 w-full max-w-md mx-4"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {editingService ? 'Edit Service' : 'Create Service'}
              </h3>
              <form onSubmit={handleSubmitForm} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Name</label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Description</label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500"
                    rows={3}
                    required
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Price</label>
                    <input
                      type="number"
                      value={formData.price}
                      onChange={(e) => setFormData({ ...formData, price: parseFloat(e.target.value) || 0 })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500"
                      min="0"
                      step="0.01"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Discount Rate (%)</label>
                    <input
                      type="number"
                      value={formData.discountRate}
                      onChange={(e) => setFormData({ ...formData, discountRate: parseFloat(e.target.value) || 0 })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500"
                      min="0"
                      max="100"
                      step="0.1"
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Manager</label>
                  <input
                    type="text"
                    value={formData.manager}
                    onChange={(e) => setFormData({ ...formData, manager: e.target.value })}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500"
                  />
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.isActive}
                    onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                    className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                  />
                  <label className="ml-2 block text-sm text-gray-900">Active</label>
                </div>
                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => setIsFormOpen(false)}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700"
                  >
                    {editingService ? 'Update' : 'Create'}
                  </button>
                </div>
              </form>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
