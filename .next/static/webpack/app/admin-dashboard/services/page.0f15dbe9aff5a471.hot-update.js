"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/services/page",{

/***/ "(app-pages-browser)/./src/components/admin/services/option-features-management.tsx":
/*!**********************************************************************!*\
  !*** ./src/components/admin/services/option-features-management.tsx ***!
  \**********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OptionFeaturesManagement: () => (/* binding */ OptionFeaturesManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,StarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,StarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,StarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,StarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,StarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,StarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,StarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,StarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* __next_internal_client_entry_do_not_use__ OptionFeaturesManagement auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction OptionFeaturesManagement(param) {\n    let { option } = param;\n    _s();\n    const [features, setFeatures] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredFeatures, setFilteredFeatures] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isFormOpen, setIsFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingFeature, setEditingFeature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [debouncedSearchTerm, setDebouncedSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('list');\n    // Function to get feature-specific icons based on feature name/type\n    const getFeatureIcon = (feature)=>{\n        const name = feature.name.toLowerCase();\n        // Design/UI features\n        if (name.includes('design') || name.includes('template') || name.includes('theme') || name.includes('layout')) {\n            return 'fa-palette text-purple-500';\n        }\n        // Content/Pages features\n        if (name.includes('page') || name.includes('content') || name.includes('blog') || name.includes('article')) {\n            return 'fa-file-alt text-blue-500';\n        }\n        // E-commerce features\n        if (name.includes('product') || name.includes('cart') || name.includes('payment') || name.includes('checkout')) {\n            return 'fa-shopping-cart text-orange-500';\n        }\n        // SEO/Marketing features\n        if (name.includes('seo') || name.includes('meta') || name.includes('analytics') || name.includes('tracking')) {\n            return 'fa-search text-green-500';\n        }\n        // Security features\n        if (name.includes('ssl') || name.includes('security') || name.includes('backup') || name.includes('protection')) {\n            return 'fa-shield-alt text-red-500';\n        }\n        // Performance features\n        if (name.includes('speed') || name.includes('cache') || name.includes('optimization') || name.includes('performance')) {\n            return 'fa-tachometer-alt text-yellow-500';\n        }\n        // Integration features\n        if (name.includes('integration') || name.includes('api') || name.includes('plugin') || name.includes('extension')) {\n            return 'fa-plug text-indigo-500';\n        }\n        // Support features\n        if (name.includes('support') || name.includes('help') || name.includes('documentation') || name.includes('training')) {\n            return 'fa-life-ring text-teal-500';\n        }\n        // Mobile features\n        if (name.includes('mobile') || name.includes('responsive') || name.includes('app')) {\n            return 'fa-mobile-alt text-pink-500';\n        }\n        // Social features\n        if (name.includes('social') || name.includes('share') || name.includes('facebook') || name.includes('twitter')) {\n            return 'fa-share-alt text-blue-400';\n        }\n        // Database/Storage features\n        if (name.includes('database') || name.includes('storage') || name.includes('hosting') || name.includes('server')) {\n            return 'fa-database text-gray-500';\n        }\n        // Default based on status\n        if (feature.isHighlighted) {\n            return 'fa-star text-yellow-400';\n        }\n        if (feature.isIncluded) {\n            return 'fa-check-circle text-green-500';\n        }\n        return 'fa-circle text-gray-400';\n    };\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        description: '',\n        isIncluded: true,\n        isHighlighted: false,\n        displayOrder: 0\n    });\n    // Debounce search term\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OptionFeaturesManagement.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"OptionFeaturesManagement.useEffect.timer\": ()=>{\n                    setDebouncedSearchTerm(searchTerm);\n                }\n            }[\"OptionFeaturesManagement.useEffect.timer\"], 300);\n            return ({\n                \"OptionFeaturesManagement.useEffect\": ()=>clearTimeout(timer)\n            })[\"OptionFeaturesManagement.useEffect\"];\n        }\n    }[\"OptionFeaturesManagement.useEffect\"], [\n        searchTerm\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OptionFeaturesManagement.useEffect\": ()=>{\n            fetchFeatures();\n        }\n    }[\"OptionFeaturesManagement.useEffect\"], [\n        option.id\n    ]);\n    // Mock data for demonstration\n    const fetchFeatures = async ()=>{\n        setLoading(true);\n        try {\n            // Mock data - replace with actual API call\n            const mockFeatures = [\n                {\n                    id: '1',\n                    optionId: option.id,\n                    name: 'Responsive Design',\n                    description: 'Mobile-friendly design that works on all devices',\n                    isIncluded: true,\n                    isHighlighted: true,\n                    displayOrder: 1,\n                    createdAt: '2024-01-15T10:00:00Z',\n                    updatedAt: '2024-01-15T10:00:00Z',\n                    option: {\n                        id: option.id,\n                        name: option.name\n                    }\n                },\n                {\n                    id: '2',\n                    optionId: option.id,\n                    name: 'SEO Optimization',\n                    description: 'Search engine optimization for better visibility',\n                    isIncluded: true,\n                    isHighlighted: false,\n                    displayOrder: 2,\n                    createdAt: '2024-01-14T10:00:00Z',\n                    updatedAt: '2024-01-14T10:00:00Z',\n                    option: {\n                        id: option.id,\n                        name: option.name\n                    }\n                },\n                {\n                    id: '3',\n                    optionId: option.id,\n                    name: 'Analytics Integration',\n                    description: 'Google Analytics and tracking setup',\n                    isIncluded: true,\n                    isHighlighted: false,\n                    displayOrder: 3,\n                    createdAt: '2024-01-13T10:00:00Z',\n                    updatedAt: '2024-01-13T10:00:00Z',\n                    option: {\n                        id: option.id,\n                        name: option.name\n                    }\n                },\n                {\n                    id: '4',\n                    optionId: option.id,\n                    name: 'Premium Support',\n                    description: '24/7 priority customer support',\n                    isIncluded: false,\n                    isHighlighted: true,\n                    displayOrder: 4,\n                    createdAt: '2024-01-12T10:00:00Z',\n                    updatedAt: '2024-01-12T10:00:00Z',\n                    option: {\n                        id: option.id,\n                        name: option.name\n                    }\n                }\n            ];\n            setFeatures(mockFeatures);\n            setFilteredFeatures(mockFeatures);\n        } catch (error) {\n            console.error('Error fetching features:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleCreateFeature = ()=>{\n        setIsFormOpen(true);\n        setEditingFeature(null);\n        setFormData({\n            name: '',\n            description: '',\n            isIncluded: true,\n            isHighlighted: false,\n            displayOrder: 0\n        });\n    };\n    const handleEditFeature = (feature)=>{\n        setEditingFeature(feature);\n        setFormData({\n            name: feature.name,\n            description: feature.description || '',\n            isIncluded: feature.isIncluded,\n            isHighlighted: feature.isHighlighted,\n            displayOrder: feature.displayOrder\n        });\n        setIsFormOpen(true);\n    };\n    const handleDeleteFeature = async (featureId)=>{\n        if (confirm('Are you sure you want to delete this feature?')) {\n            try {\n                // Mock delete - replace with actual API call\n                setFeatures((prev)=>prev.filter((feature)=>feature.id !== featureId));\n                setFilteredFeatures((prev)=>prev.filter((feature)=>feature.id !== featureId));\n            } catch (error) {\n                console.error('Error deleting feature:', error);\n            }\n        }\n    };\n    const handleSubmitForm = async (e)=>{\n        e.preventDefault();\n        try {\n            if (editingFeature) {\n                // Update existing feature\n                const updatedFeature = {\n                    ...editingFeature,\n                    ...formData\n                };\n                setFeatures((prev)=>prev.map((feature)=>feature.id === editingFeature.id ? updatedFeature : feature));\n                setFilteredFeatures((prev)=>prev.map((feature)=>feature.id === editingFeature.id ? updatedFeature : feature));\n            } else {\n                // Create new feature\n                const newFeature = {\n                    id: Date.now().toString(),\n                    optionId: option.id,\n                    ...formData,\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString(),\n                    option: {\n                        id: option.id,\n                        name: option.name\n                    }\n                };\n                setFeatures((prev)=>[\n                        ...prev,\n                        newFeature\n                    ]);\n                setFilteredFeatures((prev)=>[\n                        ...prev,\n                        newFeature\n                    ]);\n            }\n            setIsFormOpen(false);\n            setEditingFeature(null);\n        } catch (error) {\n            console.error('Error saving feature:', error);\n        }\n    };\n    const toggleFeatureIncluded = async (featureId)=>{\n        try {\n            setFeatures((prev)=>prev.map((feature)=>feature.id === featureId ? {\n                        ...feature,\n                        isIncluded: !feature.isIncluded\n                    } : feature));\n            setFilteredFeatures((prev)=>prev.map((feature)=>feature.id === featureId ? {\n                        ...feature,\n                        isIncluded: !feature.isIncluded\n                    } : feature));\n        } catch (error) {\n            console.error('Error toggling feature:', error);\n        }\n    };\n    const toggleFeatureHighlighted = async (featureId)=>{\n        try {\n            setFeatures((prev)=>prev.map((feature)=>feature.id === featureId ? {\n                        ...feature,\n                        isHighlighted: !feature.isHighlighted\n                    } : feature));\n            setFilteredFeatures((prev)=>prev.map((feature)=>feature.id === featureId ? {\n                        ...feature,\n                        isHighlighted: !feature.isHighlighted\n                    } : feature));\n        } catch (error) {\n            console.error('Error toggling feature highlight:', error);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-200 rounded w-1/4 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            ...Array(5)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-12 bg-gray-200 rounded\"\n                            }, i, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                lineNumber: 323,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n            lineNumber: 322,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900\",\n                                children: \"Option Features\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    \"Manage features for \",\n                                    option.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleCreateFeature,\n                        className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 11\n                            }, this),\n                            \"Add Feature\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                lineNumber: 338,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 flex flex-col sm:flex-row gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"Search features...\",\n                                value: searchTerm,\n                                onChange: (e)=>setSearchTerm(e.target.value),\n                                className: \"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{},\n                        className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 11\n                            }, this),\n                            \"Filters\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 364,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                lineNumber: 353,\n                columnNumber: 7\n            }, this),\n            viewMode === 'list' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 border border-gray-200 rounded-none px-4 py-3 mb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Feature\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Type\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-40\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                            lineNumber: 378,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 377,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1\",\n                        children: filteredFeatures.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 10\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                className: \"flex items-center py-2 px-4 rounded-none transition-all duration-200 border border-gray-200 \".concat(feature.isHighlighted ? 'bg-purple-50 border-purple-300' : feature.isIncluded ? 'bg-green-50 border-green-300' : 'bg-white hover:bg-gray-50'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas \".concat(getFeatureIcon(feature), \" text-base\")\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-bold text-base truncate \".concat(feature.isHighlighted ? 'text-purple-900' : feature.isIncluded ? 'text-green-900' : 'text-gray-900'),\n                                                children: feature.name\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 19\n                                            }, this),\n                                            feature.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm truncate \".concat(feature.isHighlighted ? 'text-purple-600' : feature.isIncluded ? 'text-green-600' : 'text-gray-600'),\n                                                children: feature.description\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2 py-1 rounded-none text-xs font-medium \".concat(feature.isIncluded ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                            children: feature.isIncluded ? 'Included' : 'Not Included'\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-20\",\n                                        children: feature.isHighlighted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2 py-1 rounded-none text-xs font-medium bg-purple-100 text-purple-800\",\n                                            children: \"Featured\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 w-40\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>toggleFeatureIncluded(feature.id),\n                                                className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white \".concat(feature.isIncluded ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500' : 'bg-green-600 hover:bg-green-700 focus:ring-green-500', \" focus:outline-none focus:ring-2 focus:ring-offset-2\"),\n                                                title: feature.isIncluded ? 'Mark as not included' : 'Mark as included',\n                                                children: feature.isIncluded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>toggleFeatureHighlighted(feature.id),\n                                                className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white \".concat(feature.isHighlighted ? 'bg-gray-600 hover:bg-gray-700 focus:ring-gray-500' : 'bg-purple-600 hover:bg-purple-700 focus:ring-purple-500', \" focus:outline-none focus:ring-2 focus:ring-offset-2\"),\n                                                title: feature.isHighlighted ? 'Remove highlight' : 'Highlight feature',\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleEditFeature(feature),\n                                                className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                                title: \"Edit Feature\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleDeleteFeature(feature.id),\n                                                className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                                                title: \"Delete Feature\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                    lineNumber: 493,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, feature.id, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 396,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                lineNumber: 375,\n                columnNumber: 9\n            }, this),\n            viewMode === 'table' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-none border border-gray-200 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"min-w-full divide-y divide-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Feature\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Type\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-right text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 516,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 506,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                            lineNumber: 505,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"bg-white divide-y divide-gray-200\",\n                            children: filteredFeatures.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.tr, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 10\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    className: \"transition-all duration-200 hover:bg-gray-50 \".concat(feature.isHighlighted ? 'bg-purple-50 border-l-4 border-l-purple-500' : feature.isIncluded ? 'bg-green-50 border-l-4 border-l-green-500' : 'bg-gray-50'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-0.5\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fas \".concat(getFeatureIcon(feature), \" text-base\")\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                            lineNumber: 538,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 537,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"min-w-0 flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-bold text-gray-900 truncate\",\n                                                                children: feature.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                                lineNumber: 541,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            feature.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500 truncate\",\n                                                                children: feature.description\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                                lineNumber: 543,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 540,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 536,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-0.5\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-2 py-0.5 rounded-none text-xs font-medium \".concat(feature.isIncluded ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                                children: feature.isIncluded ? 'Included' : 'Not Included'\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 549,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                            lineNumber: 548,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-0.5\",\n                                            children: feature.isHighlighted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-2 py-0.5 rounded-none text-xs font-medium bg-purple-100 text-purple-800\",\n                                                children: \"Featured\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 559,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                            lineNumber: 557,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-0.5 text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-end space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>toggleFeatureIncluded(feature.id),\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white \".concat(feature.isIncluded ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500' : 'bg-green-600 hover:bg-green-700 focus:ring-green-500', \" focus:outline-none focus:ring-2 focus:ring-offset-2\"),\n                                                        title: feature.isIncluded ? 'Mark as not included' : 'Mark as included',\n                                                        children: feature.isIncluded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                            lineNumber: 576,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                            lineNumber: 578,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 566,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>toggleFeatureHighlighted(feature.id),\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white \".concat(feature.isHighlighted ? 'bg-gray-600 hover:bg-gray-700 focus:ring-gray-500' : 'bg-purple-600 hover:bg-purple-700 focus:ring-purple-500', \" focus:outline-none focus:ring-2 focus:ring-offset-2\"),\n                                                        title: feature.isHighlighted ? 'Remove highlight' : 'Highlight feature',\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                            lineNumber: 590,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 581,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleEditFeature(feature),\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                                        title: \"Edit Feature\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                            lineNumber: 597,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 592,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleDeleteFeature(feature.id),\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                                                        title: \"Delete Feature\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                            lineNumber: 604,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 599,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 565,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, feature.id, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                    lineNumber: 523,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                            lineNumber: 521,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                    lineNumber: 504,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                lineNumber: 503,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.AnimatePresence, {\n                children: isFormOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                    onClick: ()=>setIsFormOpen(false),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            scale: 0.95,\n                            opacity: 0\n                        },\n                        animate: {\n                            scale: 1,\n                            opacity: 1\n                        },\n                        exit: {\n                            scale: 0.95,\n                            opacity: 0\n                        },\n                        className: \"bg-white rounded-none p-6 w-full max-w-md mx-4\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: editingFeature ? 'Edit Feature' : 'Create Feature'\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 632,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmitForm,\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Name\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 637,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: formData.name,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        name: e.target.value\n                                                    }),\n                                                className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 638,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 636,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Description\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: formData.description,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        description: e.target.value\n                                                    }),\n                                                className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500\",\n                                                rows: 3\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 648,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 646,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: formData.isIncluded,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                isIncluded: e.target.checked\n                                                            }),\n                                                        className: \"h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 657,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"ml-2 block text-sm text-gray-900\",\n                                                        children: \"Included\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 663,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 656,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: formData.isHighlighted,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                isHighlighted: e.target.checked\n                                                            }),\n                                                        className: \"h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 666,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"ml-2 block text-sm text-gray-900\",\n                                                        children: \"Highlighted\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 672,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 665,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 655,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>setIsFormOpen(false),\n                                                className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50\",\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 676,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"submit\",\n                                                className: \"px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-md hover:bg-purple-700\",\n                                                children: editingFeature ? 'Update' : 'Create'\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 683,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 675,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 635,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 625,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                    lineNumber: 618,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                lineNumber: 616,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n        lineNumber: 336,\n        columnNumber: 5\n    }, this);\n}\n_s(OptionFeaturesManagement, \"+/CmXx/1GB1WO+DfqCKONQSdSds=\");\n_c = OptionFeaturesManagement;\nvar _c;\n$RefreshReg$(_c, \"OptionFeaturesManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/services/option-features-management.tsx\n"));

/***/ })

});