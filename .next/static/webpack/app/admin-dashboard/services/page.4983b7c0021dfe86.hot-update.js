"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/services/page",{

/***/ "(app-pages-browser)/./src/components/admin/services/service-management.tsx":
/*!**************************************************************!*\
  !*** ./src/components/admin/services/service-management.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ServiceManagement: () => (/* binding */ ServiceManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* __next_internal_client_entry_do_not_use__ ServiceManagement auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ServiceManagement(param) {\n    let { category, selectedService, onServiceSelect } = param;\n    _s();\n    const [services, setServices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredServices, setFilteredServices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isFormOpen, setIsFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingService, setEditingService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [debouncedSearchTerm, setDebouncedSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('list');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        description: '',\n        price: 0,\n        discountRate: 0,\n        manager: '',\n        isActive: true,\n        displayOrder: 0\n    });\n    // Debounce search term\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ServiceManagement.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"ServiceManagement.useEffect.timer\": ()=>{\n                    setDebouncedSearchTerm(searchTerm);\n                }\n            }[\"ServiceManagement.useEffect.timer\"], 300);\n            return ({\n                \"ServiceManagement.useEffect\": ()=>clearTimeout(timer)\n            })[\"ServiceManagement.useEffect\"];\n        }\n    }[\"ServiceManagement.useEffect\"], [\n        searchTerm\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ServiceManagement.useEffect\": ()=>{\n            fetchServices();\n        }\n    }[\"ServiceManagement.useEffect\"], [\n        category.id\n    ]);\n    // Fetch services for the selected category\n    const fetchServices = async ()=>{\n        setLoading(true);\n        try {\n            const response = await fetch(\"/api/admin/services?categoryId=\".concat(category.id, \"&limit=100\"));\n            if (response.ok) {\n                const data = await response.json();\n                const servicesData = data.data || data.services || [];\n                setServices(servicesData);\n                setFilteredServices(servicesData);\n            } else {\n                console.error('Failed to fetch services:', response.status, response.statusText);\n                setServices([]);\n                setFilteredServices([]);\n            }\n        } catch (error) {\n            console.error('Error fetching services:', error);\n            setServices([]);\n            setFilteredServices([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleServiceSelect = (service)=>{\n        onServiceSelect(service);\n    };\n    const handleCreateService = ()=>{\n        setIsFormOpen(true);\n        setEditingService(null);\n        setFormData({\n            name: '',\n            description: '',\n            price: 0,\n            discountRate: 0,\n            manager: '',\n            isActive: true,\n            displayOrder: 0\n        });\n    };\n    const handleEditService = (service)=>{\n        setEditingService(service);\n        setFormData({\n            name: service.name,\n            description: service.description,\n            price: service.price,\n            discountRate: service.discountRate || 0,\n            manager: service.manager || '',\n            isActive: service.isActive,\n            displayOrder: service.displayOrder\n        });\n        setIsFormOpen(true);\n    };\n    const handleDeleteService = async (serviceId)=>{\n        if (confirm('Are you sure you want to delete this service?')) {\n            try {\n                const response = await fetch(\"/api/admin/services/\".concat(serviceId), {\n                    method: 'DELETE'\n                });\n                if (response.ok) {\n                    await fetchServices(); // Refresh the services list\n                } else {\n                    const errorData = await response.json();\n                    alert(errorData.message || 'Failed to delete service');\n                }\n            } catch (error) {\n                console.error('Error deleting service:', error);\n                alert('An error occurred while deleting the service');\n            }\n        }\n    };\n    const handleSubmitForm = async (e)=>{\n        e.preventDefault();\n        try {\n            const serviceData = {\n                servicename: formData.name,\n                servicedesc: formData.description,\n                categoryid: category.id,\n                price: formData.price,\n                discountrate: formData.discountRate,\n                totaldiscount: formData.discountRate > 0 ? formData.price * formData.discountRate / 100 : 0,\n                manager: formData.manager,\n                isactive: formData.isActive,\n                displayorder: formData.displayOrder,\n                iconclass: ''\n            };\n            const url = editingService ? \"/api/admin/services/\".concat(editingService.id) : '/api/admin/services';\n            const method = editingService ? 'PUT' : 'POST';\n            const response = await fetch(url, {\n                method,\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(serviceData)\n            });\n            if (response.ok) {\n                setIsFormOpen(false);\n                setEditingService(null);\n                await fetchServices(); // Refresh the services list\n            } else {\n                const errorData = await response.json();\n                alert(errorData.message || 'Failed to save service');\n            }\n        } catch (error) {\n            console.error('Error saving service:', error);\n            alert('An error occurred while saving the service');\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-200 rounded w-1/4 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            ...Array(5)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-12 bg-gray-200 rounded\"\n                            }, i, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                lineNumber: 224,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n            lineNumber: 223,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900\",\n                                children: \"Services\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    \"Manage services under \",\n                                    category.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleCreateService,\n                        className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, this),\n                            \"Add Service\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                lineNumber: 239,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 flex flex-col sm:flex-row gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"Search services...\",\n                                value: searchTerm,\n                                onChange: (e)=>setSearchTerm(e.target.value),\n                                className: \"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{},\n                        className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 11\n                            }, this),\n                            \"Filters\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                lineNumber: 254,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-none border border-gray-200 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"min-w-full divide-y divide-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Service\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Price\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Options\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-right text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"bg-white divide-y divide-gray-200\",\n                            children: filteredServices.map((service)=>{\n                                var _service__count;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.tr, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 10\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    className: \"cursor-pointer transition-all duration-200 hover:bg-gray-50 \".concat((selectedService === null || selectedService === void 0 ? void 0 : selectedService.id) === service.id ? 'bg-green-50 border-l-4 border-l-green-500' : ''),\n                                    onClick: ()=>handleServiceSelect(service),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fas fa-cogs text-green-500 text-base\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"min-w-0 flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs font-bold text-gray-900 truncate\",\n                                                                children: service.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                                lineNumber: 313,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 truncate\",\n                                                                children: service.description\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                                lineNumber: 314,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-bold text-green-600\",\n                                                        children: [\n                                                            \"$\",\n                                                            service.price.toLocaleString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    service.discountRate && service.discountRate > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-red-600 font-medium\",\n                                                        children: [\n                                                            service.discountRate,\n                                                            \"% off\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-2 py-1 rounded-none text-xs font-medium bg-blue-100 text-blue-800\",\n                                                children: [\n                                                    ((_service__count = service._count) === null || _service__count === void 0 ? void 0 : _service__count.serviceOptions) || 0,\n                                                    \" options\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-2 py-1 rounded-none text-xs font-medium \".concat(service.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                                children: service.isActive ? 'Active' : 'Inactive'\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-3 text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-end space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            handleEditService(service);\n                                                        },\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                                        title: \"Edit Service\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            handleDeleteService(service.id);\n                                                        },\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                                                        title: \"Delete Service\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                            lineNumber: 364,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, service.id, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 15\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                lineNumber: 275,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                children: isFormOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                    onClick: ()=>setIsFormOpen(false),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            scale: 0.95,\n                            opacity: 0\n                        },\n                        animate: {\n                            scale: 1,\n                            opacity: 1\n                        },\n                        exit: {\n                            scale: 0.95,\n                            opacity: 0\n                        },\n                        className: \"bg-white rounded-none p-6 w-full max-w-md mx-4\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: editingService ? 'Edit Service' : 'Create Service'\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmitForm,\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Name\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: formData.name,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        name: e.target.value\n                                                    }),\n                                                className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Description\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: formData.description,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        description: e.target.value\n                                                    }),\n                                                className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500\",\n                                                rows: 3,\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700\",\n                                                        children: \"Price\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: formData.price,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                price: parseFloat(e.target.value) || 0\n                                                            }),\n                                                        className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500\",\n                                                        min: \"0\",\n                                                        step: \"0.01\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700\",\n                                                        children: \"Discount Rate (%)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: formData.discountRate,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                discountRate: parseFloat(e.target.value) || 0\n                                                            }),\n                                                        className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500\",\n                                                        min: \"0\",\n                                                        max: \"100\",\n                                                        step: \"0.1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Manager\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: formData.manager,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        manager: e.target.value\n                                                    }),\n                                                className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: formData.isActive,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        isActive: e.target.checked\n                                                    }),\n                                                className: \"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"ml-2 block text-sm text-gray-900\",\n                                                children: \"Active\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>setIsFormOpen(false),\n                                                className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50\",\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"submit\",\n                                                className: \"px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700\",\n                                                children: editingService ? 'Update' : 'Create'\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 384,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                    lineNumber: 377,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                lineNumber: 375,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n        lineNumber: 237,\n        columnNumber: 5\n    }, this);\n}\n_s(ServiceManagement, \"lxsAPW/HYYUim+cgjcxsBi+tIuI=\");\n_c = ServiceManagement;\nvar _c;\n$RefreshReg$(_c, \"ServiceManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/services/service-management.tsx\n"));

/***/ })

});