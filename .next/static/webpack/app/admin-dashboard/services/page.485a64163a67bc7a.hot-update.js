"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/services/page",{

/***/ "(app-pages-browser)/./src/components/admin/services/service-options-management.tsx":
/*!**********************************************************************!*\
  !*** ./src/components/admin/services/service-options-management.tsx ***!
  \**********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ServiceOptionsManagement: () => (/* binding */ ServiceOptionsManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* __next_internal_client_entry_do_not_use__ ServiceOptionsManagement auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ServiceOptionsManagement(param) {\n    let { service, selectedOption, onOptionSelect } = param;\n    _s();\n    const [options, setOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredOptions, setFilteredOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isFormOpen, setIsFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingOption, setEditingOption] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [debouncedSearchTerm, setDebouncedSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('list');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        description: '',\n        price: 0,\n        discountRate: 0,\n        isActive: true\n    });\n    // Debounce search term\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ServiceOptionsManagement.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"ServiceOptionsManagement.useEffect.timer\": ()=>{\n                    setDebouncedSearchTerm(searchTerm);\n                }\n            }[\"ServiceOptionsManagement.useEffect.timer\"], 300);\n            return ({\n                \"ServiceOptionsManagement.useEffect\": ()=>clearTimeout(timer)\n            })[\"ServiceOptionsManagement.useEffect\"];\n        }\n    }[\"ServiceOptionsManagement.useEffect\"], [\n        searchTerm\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ServiceOptionsManagement.useEffect\": ()=>{\n            fetchOptions();\n        }\n    }[\"ServiceOptionsManagement.useEffect\"], [\n        service.id\n    ]);\n    // Mock data for demonstration\n    const fetchOptions = async ()=>{\n        setLoading(true);\n        try {\n            // Mock data - replace with actual API call\n            const mockOptions = [\n                {\n                    id: '1',\n                    serviceId: service.id,\n                    name: 'Basic Package',\n                    description: 'Essential features for small businesses',\n                    price: 1000,\n                    discountRate: 5,\n                    totalDiscount: 50,\n                    isActive: true,\n                    createdAt: '2024-01-15T10:00:00Z',\n                    updatedAt: '2024-01-15T10:00:00Z',\n                    service: {\n                        id: service.id,\n                        name: service.name,\n                        category: service.category\n                    },\n                    _count: {\n                        features: 5,\n                        orderDetails: 12\n                    }\n                },\n                {\n                    id: '2',\n                    serviceId: service.id,\n                    name: 'Professional Package',\n                    description: 'Advanced features for growing businesses',\n                    price: 2500,\n                    discountRate: 10,\n                    totalDiscount: 250,\n                    isActive: true,\n                    createdAt: '2024-01-14T10:00:00Z',\n                    updatedAt: '2024-01-14T10:00:00Z',\n                    service: {\n                        id: service.id,\n                        name: service.name,\n                        category: service.category\n                    },\n                    _count: {\n                        features: 12,\n                        orderDetails: 8\n                    }\n                },\n                {\n                    id: '3',\n                    serviceId: service.id,\n                    name: 'Enterprise Package',\n                    description: 'Complete solution for large organizations',\n                    price: 5000,\n                    discountRate: 15,\n                    totalDiscount: 750,\n                    isActive: true,\n                    createdAt: '2024-01-13T10:00:00Z',\n                    updatedAt: '2024-01-13T10:00:00Z',\n                    service: {\n                        id: service.id,\n                        name: service.name,\n                        category: service.category\n                    },\n                    _count: {\n                        features: 20,\n                        orderDetails: 15\n                    }\n                }\n            ];\n            setOptions(mockOptions);\n            setFilteredOptions(mockOptions);\n        } catch (error) {\n            console.error('Error fetching options:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleOptionSelect = (option)=>{\n        onOptionSelect(option);\n    };\n    const handleCreateOption = ()=>{\n        setIsFormOpen(true);\n        setEditingOption(null);\n        setFormData({\n            name: '',\n            description: '',\n            price: 0,\n            discountRate: 0,\n            isActive: true\n        });\n    };\n    const handleEditOption = (option)=>{\n        setEditingOption(option);\n        setFormData({\n            name: option.name,\n            description: option.description || '',\n            price: option.price || 0,\n            discountRate: option.discountRate || 0,\n            isActive: option.isActive\n        });\n        setIsFormOpen(true);\n    };\n    const handleDeleteOption = async (optionId)=>{\n        if (confirm('Are you sure you want to delete this option?')) {\n            try {\n                // Mock delete - replace with actual API call\n                setOptions((prev)=>prev.filter((option)=>option.id !== optionId));\n                setFilteredOptions((prev)=>prev.filter((option)=>option.id !== optionId));\n            } catch (error) {\n                console.error('Error deleting option:', error);\n            }\n        }\n    };\n    const handleSubmitForm = async (e)=>{\n        e.preventDefault();\n        try {\n            if (editingOption) {\n                // Update existing option\n                const updatedOption = {\n                    ...editingOption,\n                    ...formData\n                };\n                setOptions((prev)=>prev.map((option)=>option.id === editingOption.id ? updatedOption : option));\n                setFilteredOptions((prev)=>prev.map((option)=>option.id === editingOption.id ? updatedOption : option));\n            } else {\n                // Create new option\n                const newOption = {\n                    id: Date.now().toString(),\n                    serviceId: service.id,\n                    ...formData,\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString(),\n                    service: {\n                        id: service.id,\n                        name: service.name,\n                        category: service.category\n                    },\n                    _count: {\n                        features: 0,\n                        orderDetails: 0\n                    }\n                };\n                setOptions((prev)=>[\n                        ...prev,\n                        newOption\n                    ]);\n                setFilteredOptions((prev)=>[\n                        ...prev,\n                        newOption\n                    ]);\n            }\n            setIsFormOpen(false);\n            setEditingOption(null);\n        } catch (error) {\n            console.error('Error saving option:', error);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-200 rounded w-1/4 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            ...Array(5)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-12 bg-gray-200 rounded\"\n                            }, i, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                lineNumber: 238,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n            lineNumber: 237,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900\",\n                                children: \"Service Options\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    \"Manage options for \",\n                                    service.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleCreateOption,\n                        className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 11\n                            }, this),\n                            \"Add Option\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                lineNumber: 253,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 flex flex-col sm:flex-row gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"Search options...\",\n                                value: searchTerm,\n                                onChange: (e)=>setSearchTerm(e.target.value),\n                                className: \"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{},\n                        className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 11\n                            }, this),\n                            \"Filters\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                lineNumber: 268,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-none border border-gray-200 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"min-w-full divide-y divide-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Option\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Price\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Features\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-right text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"bg-white divide-y divide-gray-200\",\n                            children: filteredOptions.map((option)=>{\n                                var _option__count;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.tr, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 10\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    className: \"cursor-pointer transition-all duration-200 hover:bg-gray-50 \".concat((selectedOption === null || selectedOption === void 0 ? void 0 : selectedOption.id) === option.id ? 'bg-orange-50 border-l-4 border-l-orange-500' : ''),\n                                    onClick: ()=>handleOptionSelect(option),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fas fa-list-ul text-orange-500 text-base\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                            lineNumber: 324,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"min-w-0 flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs font-bold text-gray-900 truncate\",\n                                                                children: option.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                                lineNumber: 327,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            option.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 truncate\",\n                                                                children: option.description\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col\",\n                                                children: [\n                                                    option.price && option.price > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-bold text-orange-600\",\n                                                        children: [\n                                                            \"$\",\n                                                            option.price.toLocaleString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-bold text-gray-400\",\n                                                        children: \"Free\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    option.discountRate && option.discountRate > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-red-600 font-medium\",\n                                                        children: [\n                                                            option.discountRate,\n                                                            \"% off\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-2 py-1 rounded-none text-xs font-medium bg-purple-100 text-purple-800\",\n                                                children: [\n                                                    ((_option__count = option._count) === null || _option__count === void 0 ? void 0 : _option__count.features) || 0,\n                                                    \" features\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-2 py-1 rounded-none text-xs font-medium \".concat(option.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                                children: option.isActive ? 'Active' : 'Inactive'\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-3 text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-end space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            handleEditOption(option);\n                                                        },\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500\",\n                                                        title: \"Edit Option\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            handleDeleteOption(option.id);\n                                                        },\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                                                        title: \"Delete Option\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                            lineNumber: 384,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, option.id, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 15\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                    lineNumber: 290,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                lineNumber: 289,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                children: isFormOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                    onClick: ()=>setIsFormOpen(false),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            scale: 0.95,\n                            opacity: 0\n                        },\n                        animate: {\n                            scale: 1,\n                            opacity: 1\n                        },\n                        exit: {\n                            scale: 0.95,\n                            opacity: 0\n                        },\n                        className: \"bg-white rounded-none p-6 w-full max-w-md mx-4\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: editingOption ? 'Edit Option' : 'Create Option'\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmitForm,\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Name\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: formData.name,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        name: e.target.value\n                                                    }),\n                                                className: \"mt-1 block w-full border border-gray-300 rounded-none px-3 py-2 focus:outline-none focus:ring-orange-500 focus:border-orange-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Description\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: formData.description,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        description: e.target.value\n                                                    }),\n                                                className: \"mt-1 block w-full border border-gray-300 rounded-none px-3 py-2 focus:outline-none focus:ring-orange-500 focus:border-orange-500\",\n                                                rows: 3\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700\",\n                                                        children: \"Price\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: formData.price,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                price: parseFloat(e.target.value) || 0\n                                                            }),\n                                                        className: \"mt-1 block w-full border border-gray-300 rounded-none px-3 py-2 focus:outline-none focus:ring-orange-500 focus:border-orange-500\",\n                                                        min: \"0\",\n                                                        step: \"0.01\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700\",\n                                                        children: \"Discount Rate (%)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: formData.discountRate,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                discountRate: parseFloat(e.target.value) || 0\n                                                            }),\n                                                        className: \"mt-1 block w-full border border-gray-300 rounded-none px-3 py-2 focus:outline-none focus:ring-orange-500 focus:border-orange-500\",\n                                                        min: \"0\",\n                                                        max: \"100\",\n                                                        step: \"0.1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: formData.isActive,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        isActive: e.target.checked\n                                                    }),\n                                                className: \"h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded-none\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"ml-2 block text-sm text-gray-900\",\n                                                children: \"Active\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>setIsFormOpen(false),\n                                                className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-none hover:bg-gray-50\",\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"submit\",\n                                                className: \"px-4 py-2 text-sm font-medium text-white bg-orange-600 border border-transparent rounded-none hover:bg-orange-700\",\n                                                children: editingOption ? 'Update' : 'Create'\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                lineNumber: 414,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                        lineNumber: 404,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                    lineNumber: 397,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                lineNumber: 395,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n        lineNumber: 251,\n        columnNumber: 5\n    }, this);\n}\n_s(ServiceOptionsManagement, \"DM5g/dwwMXQ6N0qdpKyVN2rrVTc=\");\n_c = ServiceOptionsManagement;\nvar _c;\n$RefreshReg$(_c, \"ServiceOptionsManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/services/service-options-management.tsx\n"));

/***/ })

});