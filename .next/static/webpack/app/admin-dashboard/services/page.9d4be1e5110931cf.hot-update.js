"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/services/page",{

/***/ "(app-pages-browser)/./src/components/admin/services/service-management.tsx":
/*!**************************************************************!*\
  !*** ./src/components/admin/services/service-management.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ServiceManagement: () => (/* binding */ ServiceManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* __next_internal_client_entry_do_not_use__ ServiceManagement auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ServiceManagement(param) {\n    let { category, selectedService, onServiceSelect } = param;\n    _s();\n    const [services, setServices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredServices, setFilteredServices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isFormOpen, setIsFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingService, setEditingService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [debouncedSearchTerm, setDebouncedSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('list');\n    // Function to get service-specific icons based on service name/type\n    const getServiceIcon = (service)=>{\n        const name = service.name.toLowerCase();\n        // Web Development\n        if (name.includes('website') || name.includes('web development') || name.includes('web design')) {\n            return 'fa-globe text-blue-500';\n        }\n        // E-commerce\n        if (name.includes('ecommerce') || name.includes('online store') || name.includes('shop')) {\n            return 'fa-shopping-cart text-orange-500';\n        }\n        // Mobile App\n        if (name.includes('mobile') || name.includes('app') || name.includes('ios') || name.includes('android')) {\n            return 'fa-mobile-alt text-green-500';\n        }\n        // SEO/Marketing\n        if (name.includes('seo') || name.includes('marketing') || name.includes('social media')) {\n            return 'fa-search text-purple-500';\n        }\n        // Design\n        if (name.includes('design') || name.includes('logo') || name.includes('branding')) {\n            return 'fa-palette text-pink-500';\n        }\n        // Hosting/Domain\n        if (name.includes('hosting') || name.includes('domain') || name.includes('server')) {\n            return 'fa-server text-gray-500';\n        }\n        // Maintenance/Support\n        if (name.includes('maintenance') || name.includes('support') || name.includes('update')) {\n            return 'fa-tools text-yellow-500';\n        }\n        // Security\n        if (name.includes('security') || name.includes('ssl') || name.includes('backup')) {\n            return 'fa-shield-alt text-red-500';\n        }\n        // Analytics\n        if (name.includes('analytics') || name.includes('tracking') || name.includes('report')) {\n            return 'fa-chart-line text-teal-500';\n        }\n        // Consulting\n        if (name.includes('consulting') || name.includes('strategy') || name.includes('audit')) {\n            return 'fa-handshake text-indigo-500';\n        }\n        // Default\n        return 'fa-cogs text-green-500';\n    };\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        description: '',\n        price: 0,\n        discountRate: 0,\n        manager: '',\n        isActive: true,\n        displayOrder: 0\n    });\n    // Debounce search term\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ServiceManagement.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"ServiceManagement.useEffect.timer\": ()=>{\n                    setDebouncedSearchTerm(searchTerm);\n                }\n            }[\"ServiceManagement.useEffect.timer\"], 300);\n            return ({\n                \"ServiceManagement.useEffect\": ()=>clearTimeout(timer)\n            })[\"ServiceManagement.useEffect\"];\n        }\n    }[\"ServiceManagement.useEffect\"], [\n        searchTerm\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ServiceManagement.useEffect\": ()=>{\n            fetchServices();\n        }\n    }[\"ServiceManagement.useEffect\"], [\n        category.id\n    ]);\n    // Fetch services for the selected category\n    const fetchServices = async ()=>{\n        setLoading(true);\n        try {\n            const response = await fetch(\"/api/admin/services?categoryId=\".concat(category.id, \"&limit=100\"));\n            if (response.ok) {\n                const data = await response.json();\n                const servicesData = data.data || data.services || [];\n                setServices(servicesData);\n                setFilteredServices(servicesData);\n            } else {\n                console.error('Failed to fetch services:', response.status, response.statusText);\n                setServices([]);\n                setFilteredServices([]);\n            }\n        } catch (error) {\n            console.error('Error fetching services:', error);\n            setServices([]);\n            setFilteredServices([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleServiceSelect = (service)=>{\n        onServiceSelect(service);\n    };\n    const handleCreateService = ()=>{\n        setIsFormOpen(true);\n        setEditingService(null);\n        setFormData({\n            name: '',\n            description: '',\n            price: 0,\n            discountRate: 0,\n            manager: '',\n            isActive: true,\n            displayOrder: 0\n        });\n    };\n    const handleEditService = (service)=>{\n        setEditingService(service);\n        setFormData({\n            name: service.name,\n            description: service.description,\n            price: service.price,\n            discountRate: service.discountRate || 0,\n            manager: service.manager || '',\n            isActive: service.isActive,\n            displayOrder: service.displayOrder\n        });\n        setIsFormOpen(true);\n    };\n    const handleDeleteService = async (serviceId)=>{\n        if (confirm('Are you sure you want to delete this service?')) {\n            try {\n                const response = await fetch(\"/api/admin/services/\".concat(serviceId), {\n                    method: 'DELETE'\n                });\n                if (response.ok) {\n                    await fetchServices(); // Refresh the services list\n                } else {\n                    const errorData = await response.json();\n                    alert(errorData.message || 'Failed to delete service');\n                }\n            } catch (error) {\n                console.error('Error deleting service:', error);\n                alert('An error occurred while deleting the service');\n            }\n        }\n    };\n    const handleSubmitForm = async (e)=>{\n        e.preventDefault();\n        try {\n            const serviceData = {\n                servicename: formData.name,\n                servicedesc: formData.description,\n                categoryid: category.id,\n                price: formData.price,\n                discountrate: formData.discountRate,\n                totaldiscount: formData.discountRate > 0 ? formData.price * formData.discountRate / 100 : 0,\n                manager: formData.manager,\n                isactive: formData.isActive,\n                displayorder: formData.displayOrder,\n                iconclass: ''\n            };\n            const url = editingService ? \"/api/admin/services/\".concat(editingService.id) : '/api/admin/services';\n            const method = editingService ? 'PUT' : 'POST';\n            const response = await fetch(url, {\n                method,\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(serviceData)\n            });\n            if (response.ok) {\n                setIsFormOpen(false);\n                setEditingService(null);\n                await fetchServices(); // Refresh the services list\n            } else {\n                const errorData = await response.json();\n                alert(errorData.message || 'Failed to save service');\n            }\n        } catch (error) {\n            console.error('Error saving service:', error);\n            alert('An error occurred while saving the service');\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-200 rounded w-1/4 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            ...Array(5)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-12 bg-gray-200 rounded\"\n                            }, i, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                lineNumber: 272,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n            lineNumber: 271,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900\",\n                                children: \"Services\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    \"Manage services under \",\n                                    category.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleCreateService,\n                        className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 11\n                            }, this),\n                            \"Add Service\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                lineNumber: 287,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 flex flex-col sm:flex-row gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"Search services...\",\n                                value: searchTerm,\n                                onChange: (e)=>setSearchTerm(e.target.value),\n                                className: \"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{},\n                        className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 11\n                            }, this),\n                            \"Filters\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                lineNumber: 302,\n                columnNumber: 7\n            }, this),\n            viewMode === 'list' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 border border-gray-200 rounded-none px-4 py-3 mb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Service\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Price\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Options\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-32\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                            lineNumber: 327,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1\",\n                        children: filteredServices.map((service)=>{\n                            var _service__count;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 10\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                className: \"flex items-center py-2 px-4 rounded-none cursor-pointer transition-all duration-200 border border-gray-200 \".concat((selectedService === null || selectedService === void 0 ? void 0 : selectedService.id) === service.id ? 'bg-green-50 border-green-300' : 'bg-white hover:bg-gray-50'),\n                                onClick: ()=>handleServiceSelect(service),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas \".concat(getServiceIcon(service), \" text-base\")\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-bold text-base truncate \".concat((selectedService === null || selectedService === void 0 ? void 0 : selectedService.id) === service.id ? 'text-green-900' : 'text-gray-900'),\n                                                children: service.name\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm truncate \".concat((selectedService === null || selectedService === void 0 ? void 0 : selectedService.id) === service.id ? 'text-green-600' : 'text-gray-600'),\n                                                children: service.description\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-24\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs font-bold text-green-600\",\n                                                children: [\n                                                    \"$\",\n                                                    service.price.toLocaleString()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 19\n                                            }, this),\n                                            service.discountRate && service.discountRate > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-red-600 font-medium\",\n                                                children: [\n                                                    service.discountRate,\n                                                    \"% off\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2 py-1 rounded-none text-xs font-medium bg-blue-100 text-blue-800\",\n                                            children: ((_service__count = service._count) === null || _service__count === void 0 ? void 0 : _service__count.serviceOptions) || 0\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2 py-1 rounded-none text-xs font-medium \".concat(service.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                            children: service.isActive ? 'Active' : 'Inactive'\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 w-32\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    handleEditService(service);\n                                                },\n                                                className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                                title: \"Edit Service\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    handleDeleteService(service.id);\n                                                },\n                                                className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                                                title: \"Delete Service\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, service.id, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 348,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                lineNumber: 324,\n                columnNumber: 9\n            }, this),\n            viewMode === 'table' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-none border border-gray-200 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"min-w-full divide-y divide-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Service\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Price\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Options\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-right text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 439,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                            lineNumber: 438,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"bg-white divide-y divide-gray-200\",\n                            children: filteredServices.map((service)=>{\n                                var _service__count;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.tr, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 10\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    className: \"cursor-pointer transition-all duration-200 hover:bg-gray-50 \".concat((selectedService === null || selectedService === void 0 ? void 0 : selectedService.id) === service.id ? 'bg-green-50 border-l-4 border-l-green-500' : ''),\n                                    onClick: ()=>handleServiceSelect(service),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fas \".concat(getServiceIcon(service), \" text-base\")\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                            lineNumber: 471,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 470,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"min-w-0 flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-bold text-gray-900 truncate\",\n                                                                children: service.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                                lineNumber: 474,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500 truncate\",\n                                                                children: service.description\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-bold text-green-600\",\n                                                        children: [\n                                                            \"$\",\n                                                            service.price.toLocaleString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    service.discountRate && service.discountRate > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-red-600 font-medium\",\n                                                        children: [\n                                                            service.discountRate,\n                                                            \"% off\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 485,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-2 py-1 rounded-none text-xs font-medium bg-blue-100 text-blue-800\",\n                                                children: [\n                                                    ((_service__count = service._count) === null || _service__count === void 0 ? void 0 : _service__count.serviceOptions) || 0,\n                                                    \" options\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-2 py-1 rounded-none text-xs font-medium \".concat(service.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                                children: service.isActive ? 'Active' : 'Inactive'\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                            lineNumber: 496,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-1 text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-end space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            handleEditService(service);\n                                                        },\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                                        title: \"Edit Service\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                            lineNumber: 515,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            handleDeleteService(service.id);\n                                                        },\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                                                        title: \"Delete Service\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 506,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                            lineNumber: 505,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, service.id, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 15\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                    lineNumber: 437,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                lineNumber: 436,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                children: isFormOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                    onClick: ()=>setIsFormOpen(false),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            scale: 0.95,\n                            opacity: 0\n                        },\n                        animate: {\n                            scale: 1,\n                            opacity: 1\n                        },\n                        exit: {\n                            scale: 0.95,\n                            opacity: 0\n                        },\n                        className: \"bg-white rounded-none p-6 w-full max-w-md mx-4\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: editingService ? 'Edit Service' : 'Create Service'\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 553,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmitForm,\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Name\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 558,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: formData.name,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        name: e.target.value\n                                                    }),\n                                                className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 559,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 557,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Description\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 568,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: formData.description,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        description: e.target.value\n                                                    }),\n                                                className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500\",\n                                                rows: 3,\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 569,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 567,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700\",\n                                                        children: \"Price\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 579,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: formData.price,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                price: parseFloat(e.target.value) || 0\n                                                            }),\n                                                        className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500\",\n                                                        min: \"0\",\n                                                        step: \"0.01\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 580,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 578,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700\",\n                                                        children: \"Discount Rate (%)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 591,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: formData.discountRate,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                discountRate: parseFloat(e.target.value) || 0\n                                                            }),\n                                                        className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500\",\n                                                        min: \"0\",\n                                                        max: \"100\",\n                                                        step: \"0.1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                        lineNumber: 592,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 590,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 577,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Manager\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 604,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: formData.manager,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        manager: e.target.value\n                                                    }),\n                                                className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 605,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 603,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: formData.isActive,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        isActive: e.target.checked\n                                                    }),\n                                                className: \"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 613,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"ml-2 block text-sm text-gray-900\",\n                                                children: \"Active\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 619,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 612,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>setIsFormOpen(false),\n                                                className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50\",\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 622,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"submit\",\n                                                className: \"px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700\",\n                                                children: editingService ? 'Update' : 'Create'\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                                lineNumber: 629,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                        lineNumber: 621,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                                lineNumber: 556,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                        lineNumber: 546,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                    lineNumber: 539,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n                lineNumber: 537,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-management.tsx\",\n        lineNumber: 285,\n        columnNumber: 5\n    }, this);\n}\n_s(ServiceManagement, \"lxsAPW/HYYUim+cgjcxsBi+tIuI=\");\n_c = ServiceManagement;\nvar _c;\n$RefreshReg$(_c, \"ServiceManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/services/service-management.tsx\n"));

/***/ })

});