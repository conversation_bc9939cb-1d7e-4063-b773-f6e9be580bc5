"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/services/page",{

/***/ "(app-pages-browser)/./src/components/admin/services/services-management.tsx":
/*!***************************************************************!*\
  !*** ./src/components/admin/services/services-management.tsx ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ServicesManagement: () => (/* binding */ ServicesManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronRightIcon_CogIcon_FolderIcon_ListBulletIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRightIcon,CogIcon,FolderIcon,ListBulletIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FolderIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronRightIcon_CogIcon_FolderIcon_ListBulletIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRightIcon,CogIcon,FolderIcon,ListBulletIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronRightIcon_CogIcon_FolderIcon_ListBulletIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRightIcon,CogIcon,FolderIcon,ListBulletIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronRightIcon_CogIcon_FolderIcon_ListBulletIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRightIcon,CogIcon,FolderIcon,ListBulletIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronRightIcon_CogIcon_FolderIcon_ListBulletIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRightIcon,CogIcon,FolderIcon,ListBulletIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js\");\n/* harmony import */ var _category_management__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./category-management */ \"(app-pages-browser)/./src/components/admin/services/category-management.tsx\");\n/* harmony import */ var _service_management__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./service-management */ \"(app-pages-browser)/./src/components/admin/services/service-management.tsx\");\n/* harmony import */ var _service_options_management__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./service-options-management */ \"(app-pages-browser)/./src/components/admin/services/service-options-management.tsx\");\n/* harmony import */ var _option_features_management__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./option-features-management */ \"(app-pages-browser)/./src/components/admin/services/option-features-management.tsx\");\n/* __next_internal_client_entry_do_not_use__ ServicesManagement auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Memoized section navigation component\nconst SectionNavigation = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { sections, onSectionChange } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 min-w-[640px] md:min-w-0\",\n            children: sections.map((section)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                    onClick: ()=>onSectionChange(section.id),\n                    disabled: section.disabled,\n                    className: \"p-4 rounded-lg border-2 transition-all duration-200 text-left \".concat(section.isActive ? 'border-blue-500 bg-blue-50 shadow-md' : section.disabled ? 'border-gray-200 bg-gray-50 cursor-not-allowed opacity-50' : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-sm'),\n                    whileHover: !section.disabled ? {\n                        scale: 1.02\n                    } : undefined,\n                    whileTap: !section.disabled ? {\n                        scale: 0.98\n                    } : undefined,\n                    \"aria-label\": \"Navigate to \".concat(section.title, \" section\"),\n                    \"aria-describedby\": \"\".concat(section.id, \"-description\"),\n                    \"aria-current\": section.isActive ? 'page' : undefined,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 rounded-lg \".concat(section.color, \" \").concat(section.disabled ? 'opacity-50' : ''),\n                                children: [\n                                    section.id === 'categories' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRightIcon_CogIcon_FolderIcon_ListBulletIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-5 w-5 text-white\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 47\n                                    }, undefined),\n                                    section.id === 'services' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRightIcon_CogIcon_FolderIcon_ListBulletIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-5 w-5 text-white\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 45\n                                    }, undefined),\n                                    section.id === 'options' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRightIcon_CogIcon_FolderIcon_ListBulletIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-5 w-5 text-white\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 44\n                                    }, undefined),\n                                    section.id === 'features' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRightIcon_CogIcon_FolderIcon_ListBulletIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-5 w-5 text-white\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 45\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold \".concat(section.isActive ? 'text-blue-900' : section.disabled ? 'text-gray-400' : 'text-gray-900'),\n                                        children: section.title\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        id: \"\".concat(section.id, \"-description\"),\n                                        className: \"text-sm \".concat(section.isActive ? 'text-blue-600' : section.disabled ? 'text-gray-400' : 'text-gray-500'),\n                                        children: section.description\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 11\n                    }, undefined)\n                }, section.id, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n            lineNumber: 100,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n        lineNumber: 99,\n        columnNumber: 3\n    }, undefined);\n});\n_c = SectionNavigation;\nSectionNavigation.displayName = 'SectionNavigation';\n// Memoized breadcrumb component\nconst Breadcrumb = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { selectedCategory, selectedService, selectedOption } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mt-4 flex items-center space-x-2 text-sm\",\n        \"aria-label\": \"Navigation breadcrumb\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-gray-500\",\n                children: \"Current Path:\"\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                lineNumber: 156,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex items-center space-x-1\",\n                \"aria-label\": \"Breadcrumb\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"font-medium text-blue-600\",\n                        children: \"Categories\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 7\n                    }, undefined),\n                    selectedCategory && selectedCategory.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRightIcon_CogIcon_FolderIcon_ListBulletIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-4 w-4 text-gray-400\",\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-green-600\",\n                                children: selectedCategory.name\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    selectedService && selectedService.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRightIcon_CogIcon_FolderIcon_ListBulletIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-4 w-4 text-gray-400\",\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-orange-600\",\n                                children: selectedService.name\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    selectedOption && selectedOption.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRightIcon_CogIcon_FolderIcon_ListBulletIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-4 w-4 text-gray-400\",\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-purple-600\",\n                                children: selectedOption.name\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                lineNumber: 157,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n        lineNumber: 155,\n        columnNumber: 3\n    }, undefined);\n});\n_c1 = Breadcrumb;\nBreadcrumb.displayName = 'Breadcrumb';\n// Memoized header component\nconst Header = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { selectedCategory, selectedService, selectedOption } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Services Management System\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Manage your service hierarchy: Categories → Services → Options → Features\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 text-sm text-gray-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Hierarchical Management System\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 9\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                lineNumber: 189,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Breadcrumb, {\n                selectedCategory: selectedCategory,\n                selectedService: selectedService,\n                selectedOption: selectedOption\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                lineNumber: 200,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n        lineNumber: 188,\n        columnNumber: 3\n    }, undefined);\n});\n_c2 = Header;\nHeader.displayName = 'Header';\nfunction ServicesManagement() {\n    var _sections_find;\n    _s();\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('categories');\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedService, setSelectedService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedOption, setSelectedOption] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const sections = [\n        {\n            id: 'categories',\n            title: 'Categories',\n            description: 'Manage service categories and subcategories',\n            color: 'bg-blue-500',\n            isActive: activeSection === 'categories',\n            disabled: false\n        },\n        {\n            id: 'services',\n            title: 'Services',\n            description: 'Manage services under categories',\n            color: 'bg-green-500',\n            isActive: activeSection === 'services',\n            disabled: !selectedCategory\n        },\n        {\n            id: 'options',\n            title: 'Service Options',\n            description: 'Manage options for services',\n            color: 'bg-orange-500',\n            isActive: activeSection === 'options',\n            disabled: !selectedService\n        },\n        {\n            id: 'features',\n            title: 'Option Features',\n            description: 'Manage features for service options',\n            color: 'bg-purple-500',\n            isActive: activeSection === 'features',\n            disabled: !selectedOption\n        }\n    ];\n    const handleSectionChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ServicesManagement.useCallback[handleSectionChange]\": (sectionId)=>{\n            var _sections_find;\n            if ((_sections_find = sections.find({\n                \"ServicesManagement.useCallback[handleSectionChange]\": (s)=>s.id === sectionId\n            }[\"ServicesManagement.useCallback[handleSectionChange]\"])) === null || _sections_find === void 0 ? void 0 : _sections_find.disabled) return;\n            setActiveSection(sectionId);\n        }\n    }[\"ServicesManagement.useCallback[handleSectionChange]\"], [\n        sections\n    ]);\n    const handleCategorySelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ServicesManagement.useCallback[handleCategorySelect]\": (category)=>{\n            setSelectedCategory(category);\n            setSelectedService(null);\n            setSelectedOption(null);\n            if (category && activeSection === 'categories') {\n                setActiveSection('services');\n            }\n        }\n    }[\"ServicesManagement.useCallback[handleCategorySelect]\"], [\n        activeSection\n    ]);\n    const handleServiceSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ServicesManagement.useCallback[handleServiceSelect]\": (service)=>{\n            setSelectedService(service);\n            setSelectedOption(null);\n            if (service && activeSection === 'services') {\n                setActiveSection('options');\n            }\n        }\n    }[\"ServicesManagement.useCallback[handleServiceSelect]\"], [\n        activeSection\n    ]);\n    const handleOptionSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ServicesManagement.useCallback[handleOptionSelect]\": (option)=>{\n            setSelectedOption(option);\n            if (option && activeSection === 'options') {\n                setActiveSection('features');\n            }\n        }\n    }[\"ServicesManagement.useCallback[handleOptionSelect]\"], [\n        activeSection\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Header, {\n                selectedCategory: selectedCategory,\n                selectedService: selectedService,\n                selectedOption: selectedOption\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionNavigation, {\n                sections: sections,\n                onSectionChange: handleSectionChange\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                lineNumber: 287,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm border border-gray-200 overflow-x-auto\",\n                role: \"main\",\n                \"aria-label\": \"\".concat((_sections_find = sections.find((s)=>s.isActive)) === null || _sections_find === void 0 ? void 0 : _sections_find.title, \" management section\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.AnimatePresence, {\n                    mode: \"wait\",\n                    children: [\n                        activeSection === 'categories' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            transition: {\n                                duration: 0.2\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_category_management__WEBPACK_IMPORTED_MODULE_2__.CategoryManagement, {\n                                selectedCategory: selectedCategory,\n                                onCategorySelect: handleCategorySelect\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 15\n                            }, this)\n                        }, \"categories\", false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                            lineNumber: 300,\n                            columnNumber: 13\n                        }, this),\n                        activeSection === 'services' && selectedCategory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            transition: {\n                                duration: 0.2\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_service_management__WEBPACK_IMPORTED_MODULE_3__.ServiceManagement, {\n                                category: selectedCategory,\n                                selectedService: selectedService,\n                                onServiceSelect: handleServiceSelect\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 15\n                            }, this)\n                        }, \"services\", false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 13\n                        }, this),\n                        activeSection === 'options' && selectedService && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            transition: {\n                                duration: 0.2\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_service_options_management__WEBPACK_IMPORTED_MODULE_4__.ServiceOptionsManagement, {\n                                service: selectedService,\n                                selectedOption: selectedOption,\n                                onOptionSelect: handleOptionSelect\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 15\n                            }, this)\n                        }, \"options\", false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 13\n                        }, this),\n                        activeSection === 'features' && selectedOption && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            transition: {\n                                duration: 0.2\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_option_features_management__WEBPACK_IMPORTED_MODULE_5__.OptionFeaturesManagement, {\n                                option: selectedOption\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 15\n                            }, this)\n                        }, \"features\", false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                    lineNumber: 298,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/services-management.tsx\",\n        lineNumber: 280,\n        columnNumber: 5\n    }, this);\n}\n_s(ServicesManagement, \"lBxHOr9xVTylppf0HgUe9JN7rbk=\");\n_c3 = ServicesManagement;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"SectionNavigation\");\n$RefreshReg$(_c1, \"Breadcrumb\");\n$RefreshReg$(_c2, \"Header\");\n$RefreshReg$(_c3, \"ServicesManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/services/services-management.tsx\n"));

/***/ })

});