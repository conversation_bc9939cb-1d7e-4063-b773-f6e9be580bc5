"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/services/page",{

/***/ "(app-pages-browser)/./src/components/admin/services/category-management.tsx":
/*!***************************************************************!*\
  !*** ./src/components/admin/services/category-management.tsx ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CategoryManagement: () => (/* binding */ CategoryManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeSlashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FolderIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _category_header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./category-header */ \"(app-pages-browser)/./src/components/admin/services/category-header.tsx\");\n/* __next_internal_client_entry_do_not_use__ CategoryManagement auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction CategoryManagement(param) {\n    let { selectedCategory, onCategorySelect } = param;\n    var _this = this;\n    _s();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredCategories, setFilteredCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isFormOpen, setIsFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingCategory, setEditingCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [expandedCategories, setExpandedCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentFilters, setCurrentFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('list');\n    const [density, setDensity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('comfortable');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        description: '',\n        parentId: '',\n        isActive: true,\n        displayOrder: 0\n    });\n    const filters = [\n        {\n            key: 'status',\n            label: 'Status',\n            type: 'select',\n            options: [\n                {\n                    value: '',\n                    label: 'All Status'\n                },\n                {\n                    value: 'active',\n                    label: 'Active'\n                },\n                {\n                    value: 'inactive',\n                    label: 'Inactive'\n                }\n            ]\n        },\n        {\n            key: 'parent',\n            label: 'Parent Category',\n            type: 'select',\n            options: [\n                {\n                    value: '',\n                    label: 'All Categories'\n                },\n                {\n                    value: 'root',\n                    label: 'Root Categories'\n                },\n                {\n                    value: 'sub',\n                    label: 'Sub Categories'\n                }\n            ]\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CategoryManagement.useEffect\": ()=>{\n            fetchCategories();\n        }\n    }[\"CategoryManagement.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CategoryManagement.useEffect\": ()=>{\n            filterAndSortCategories();\n        }\n    }[\"CategoryManagement.useEffect\"], [\n        categories,\n        searchQuery,\n        currentFilters\n    ]);\n    const filterAndSortCategories = ()=>{\n        let filtered = [\n            ...categories\n        ];\n        // Apply search filter\n        if (searchQuery.trim()) {\n            const searchLower = searchQuery.toLowerCase();\n            filtered = filtered.filter((category)=>category.name.toLowerCase().includes(searchLower) || category.description && category.description.toLowerCase().includes(searchLower));\n        }\n        // Apply status filter\n        if (currentFilters.status) {\n            if (currentFilters.status === 'active') {\n                filtered = filtered.filter((category)=>category.isActive);\n            } else if (currentFilters.status === 'inactive') {\n                filtered = filtered.filter((category)=>!category.isActive);\n            }\n        }\n        // Apply parent filter\n        if (currentFilters.parent) {\n            if (currentFilters.parent === 'root') {\n                filtered = filtered.filter((category)=>!category.parentId);\n            } else if (currentFilters.parent === 'sub') {\n                filtered = filtered.filter((category)=>category.parentId);\n            }\n        }\n        setFilteredCategories(filtered);\n    };\n    const buildCategoryTree = (flatCategories)=>{\n        const categoryMap = new Map();\n        const rootCategories = [];\n        // Transform and create map\n        flatCategories.forEach((cat)=>{\n            const category = {\n                id: String(cat.id),\n                name: cat.categname || cat.name,\n                description: cat.categdesc || cat.description,\n                parentId: cat.parentid ? String(cat.parentid) : undefined,\n                isActive: cat.isactive,\n                displayOrder: cat.displayorder || 0,\n                children: [],\n                _count: cat._count\n            };\n            categoryMap.set(category.id, category);\n        });\n        // Build tree structure\n        categoryMap.forEach((category)=>{\n            if (category.parentId && categoryMap.has(category.parentId)) {\n                categoryMap.get(category.parentId).children.push(category);\n            } else {\n                rootCategories.push(category);\n            }\n        });\n        // Sort by display order\n        const sortCategories = (cats)=>{\n            cats.sort((a, b)=>a.displayOrder - b.displayOrder);\n            cats.forEach((cat)=>{\n                if (cat.children) {\n                    sortCategories(cat.children);\n                }\n            });\n        };\n        sortCategories(rootCategories);\n        return rootCategories;\n    };\n    const fetchCategories = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch('/api/admin/categories?limit=100');\n            if (response.ok) {\n                const data = await response.json();\n                const categoriesData = data.data || data.categories || [];\n                setCategories(buildCategoryTree(categoriesData));\n            } else {\n                console.error('Failed to fetch categories:', response.status, response.statusText);\n                setCategories([]);\n            }\n        } catch (error) {\n            console.error('Error fetching categories:', error);\n            setCategories([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleEdit = (category)=>{\n        setEditingCategory(category);\n        setFormData({\n            name: category.name,\n            description: category.description || '',\n            parentId: category.parentId || '',\n            isActive: category.isActive,\n            displayOrder: category.displayOrder\n        });\n        setIsFormOpen(true);\n    };\n    const handleDelete = async (category)=>{\n        if (!confirm('Are you sure you want to delete \"'.concat(category.name, '\"?'))) return;\n        try {\n            const response = await fetch(\"/api/admin/categories/\".concat(category.id), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                await fetchCategories();\n                if ((selectedCategory === null || selectedCategory === void 0 ? void 0 : selectedCategory.id) === category.id) {\n                    onCategorySelect(null);\n                }\n            } else {\n                const errorData = await response.json();\n                alert(errorData.message || 'Failed to delete category');\n            }\n        } catch (error) {\n            console.error('Error deleting category:', error);\n            alert('An error occurred while deleting the category');\n        }\n    };\n    const handleToggleActive = async (category)=>{\n        try {\n            const response = await fetch(\"/api/admin/categories/\".concat(category.id), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    categname: category.name,\n                    categdesc: category.description,\n                    parentid: category.parentId ? Number(category.parentId) : 0,\n                    isactive: !category.isActive,\n                    displayorder: category.displayOrder\n                })\n            });\n            if (response.ok) {\n                fetchCategories();\n            }\n        } catch (error) {\n            console.error('Error toggling category status:', error);\n        }\n    };\n    const toggleExpanded = (categoryId)=>{\n        const newExpanded = new Set(expandedCategories);\n        if (newExpanded.has(categoryId)) {\n            newExpanded.delete(categoryId);\n        } else {\n            newExpanded.add(categoryId);\n        }\n        setExpandedCategories(newExpanded);\n    };\n    const renderCategory = function(category) {\n        let level = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        var _categories_find, _category_children;\n        const isExpanded = expandedCategories.has(category.id);\n        const hasChildren = category.children && category.children.length > 0;\n        const isSelected = (selectedCategory === null || selectedCategory === void 0 ? void 0 : selectedCategory.id) === category.id;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"select-none\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between py-2 px-4 rounded-none cursor-pointer transition-colors border border-gray-200 \".concat(isSelected ? 'bg-blue-50 border-blue-300' : 'bg-white hover:bg-gray-50'),\n                    onClick: ()=>onCategorySelect(category),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-6\",\n                            style: {\n                                marginLeft: \"\".concat(level * 20, \"px\")\n                            },\n                            children: hasChildren ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: (e)=>{\n                                    e.stopPropagation();\n                                    toggleExpanded(category.id);\n                                },\n                                className: \"p-1 hover:bg-gray-200 rounded-none\",\n                                children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-4 w-4 text-gray-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 19\n                                }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-4 w-4 text-gray-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 19\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 15\n                            }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-6\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-6\",\n                            children: hasChildren ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas fa-layer-group text-blue-500 text-lg\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 15\n                            }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas fa-tag text-orange-500 text-lg\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-bold text-base truncate \".concat(isSelected ? 'text-blue-900' : 'text-gray-900'),\n                                    children: category.name\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, _this),\n                                category.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm truncate \".concat(isSelected ? 'text-blue-600' : 'text-gray-600'),\n                                    children: category.description\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 15\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-32\",\n                            children: category.parentId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-600 truncate block\",\n                                children: ((_categories_find = categories.find((c)=>c.id === category.parentId)) === null || _categories_find === void 0 ? void 0 : _categories_find.name) || 'Unknown'\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 15\n                            }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-400 italic\",\n                                children: \"Root\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-24\",\n                            children: category._count && typeof category._count.services === 'number' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"bg-blue-100 text-blue-800 px-2 py-1 rounded-none text-sm font-medium\",\n                                children: category._count.services\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 327,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-flex items-center px-2 py-1 rounded-none text-xs font-medium \".concat(category.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                children: category.isActive ? 'Active' : 'Inactive'\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 13\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 w-32\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleEdit(category);\n                                    },\n                                    className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                    title: \"Edit category\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-3 w-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 21\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 19\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleToggleActive(category);\n                                    },\n                                    className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white \".concat(category.isActive ? 'bg-orange-600 hover:bg-orange-700 focus:ring-orange-500' : 'bg-green-600 hover:bg-green-700 focus:ring-green-500', \" focus:outline-none focus:ring-2 focus:ring-offset-2\"),\n                                    title: category.isActive ? 'Deactivate category' : 'Activate category',\n                                    children: category.isActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-3 w-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 23\n                                    }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-3 w-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 23\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 19\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleDelete(category);\n                                    },\n                                    className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                                    title: \"Delete category\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-3 w-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 21\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 19\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 11\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 265,\n                    columnNumber: 9\n                }, _this),\n                hasChildren && isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"ml-6\",\n                    children: (_category_children = category.children) === null || _category_children === void 0 ? void 0 : _category_children.map((child)=>renderCategory(child, level + 1))\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 393,\n                    columnNumber: 11\n                }, _this)\n            ]\n        }, category.id, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n            lineNumber: 264,\n            columnNumber: 7\n        }, _this);\n    };\n    const handleCreateClick = ()=>{\n        setIsFormOpen(true);\n        setEditingCategory(null);\n        setFormData({\n            name: '',\n            description: '',\n            parentId: '',\n            isActive: true,\n            displayOrder: 0\n        });\n    };\n    const handleFiltersChange = (newFilters)=>{\n        setCurrentFilters(newFilters);\n    };\n    const renderCategoryCard = function(category) {\n        let isLargeCard = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        var _category_children;\n        const isSelected = (selectedCategory === null || selectedCategory === void 0 ? void 0 : selectedCategory.id) === category.id;\n        const hasChildren = category.children && category.children.length > 0;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-50 border rounded-none cursor-pointer transition-all duration-200 \".concat(isSelected ? 'border-blue-500 shadow-md bg-blue-50' : 'border-gray-200 hover:border-gray-300 hover:shadow-sm hover:bg-gray-100', \" \").concat(isLargeCard ? 'p-6' : 'p-4'),\n            onClick: ()=>onCategorySelect(category),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 mb-2\",\n                                children: [\n                                    hasChildren ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-5 w-5 text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 17\n                                    }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-5 w-5 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 17\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold \".concat(isSelected ? 'text-blue-900' : 'text-gray-900'),\n                                        children: category.name\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 15\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(category.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'),\n                                        children: category.isActive ? 'Active' : 'Inactive'\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 15\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 433,\n                                columnNumber: 13\n                            }, _this),\n                            category.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm mb-3 \".concat(isSelected ? 'text-blue-600' : 'text-gray-600'),\n                                children: category.description\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 15\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 text-xs text-gray-500\",\n                                children: [\n                                    category._count && typeof category._count.services === 'number' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            category._count.services,\n                                            \" services\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 17\n                                    }, _this),\n                                    hasChildren && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            (_category_children = category.children) === null || _category_children === void 0 ? void 0 : _category_children.length,\n                                            \" subcategories\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 462,\n                                        columnNumber: 17\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Order: \",\n                                            category.displayOrder\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 15\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 13\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                        lineNumber: 432,\n                        columnNumber: 11\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-1 ml-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: (e)=>{\n                                    e.stopPropagation();\n                                    handleEdit(category);\n                                },\n                                className: \"text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded p-1\",\n                                title: \"Edit category\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 477,\n                                    columnNumber: 15\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 13\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: (e)=>{\n                                    e.stopPropagation();\n                                    handleToggleActive(category);\n                                },\n                                className: \"rounded transition-colors p-1 \".concat(category.isActive ? 'text-green-600 hover:text-green-700 hover:bg-green-50' : 'text-gray-400 hover:text-gray-600 hover:bg-gray-50'),\n                                title: category.isActive ? 'Deactivate category' : 'Activate category',\n                                children: category.isActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 493,\n                                    columnNumber: 17\n                                }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 495,\n                                    columnNumber: 17\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 480,\n                                columnNumber: 13\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: (e)=>{\n                                    e.stopPropagation();\n                                    handleDelete(category);\n                                },\n                                className: \"text-gray-400 hover:text-red-600 hover:bg-red-50 rounded p-1\",\n                                title: \"Delete category\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 507,\n                                    columnNumber: 15\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 499,\n                                columnNumber: 13\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                        lineNumber: 468,\n                        columnNumber: 11\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                lineNumber: 431,\n                columnNumber: 9\n            }, _this)\n        }, category.id, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n            lineNumber: 422,\n            columnNumber: 7\n        }, _this);\n    };\n    const getAllCategories = (cats)=>{\n        let all = [];\n        cats.forEach((cat)=>{\n            all.push(cat);\n            if (cat.children && cat.children.length > 0) {\n                all = all.concat(getAllCategories(cat.children));\n            }\n        });\n        return all;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_category_header__WEBPACK_IMPORTED_MODULE_2__.CategoryHeader, {\n                title: \"Categories\",\n                description: \"Manage service categories and subcategories\",\n                searchPlaceholder: \"Search categories by name or description...\",\n                searchQuery: searchQuery,\n                onSearchChange: setSearchQuery,\n                enableSearch: true,\n                enableFilters: true,\n                enableViewControls: true,\n                enableCreate: true,\n                onCreateClick: handleCreateClick,\n                createButtonText: \"Add Category\",\n                viewMode: viewMode,\n                onViewModeChange: setViewMode,\n                filters: filters,\n                onFiltersChange: handleFiltersChange,\n                currentFilters: currentFilters,\n                itemCount: filteredCategories.length,\n                totalItems: categories.length\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                lineNumber: 528,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm border border-gray-200\",\n                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 553,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-gray-600\",\n                            children: \"Loading categories...\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 554,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 552,\n                    columnNumber: 11\n                }, this) : filteredCategories.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 558,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                            children: \"No categories found\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 559,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: searchQuery || Object.keys(currentFilters).some((key)=>currentFilters[key]) ? 'Try adjusting your search terms or filters.' : 'Get started by creating your first category.'\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 560,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleCreateClick,\n                            className: \"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 569,\n                                    columnNumber: 15\n                                }, this),\n                                \"Add Category\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 565,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 557,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4\",\n                    children: [\n                        viewMode === 'table' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-none border border-gray-200 overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"min-w-full divide-y divide-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        className: \"bg-gray-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Category\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 580,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Parent\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 583,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Services\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 586,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 589,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-4 py-3 text-right text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 592,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                            lineNumber: 579,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 578,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        className: \"bg-white divide-y divide-gray-200\",\n                                        children: getAllCategories(filteredCategories).map((category)=>{\n                                            var _categories_find, _category__count;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.tr, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 10\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                className: \"cursor-pointer transition-all duration-200 hover:bg-gray-50 \".concat((selectedCategory === null || selectedCategory === void 0 ? void 0 : selectedCategory.id) === category.id ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''),\n                                                onClick: ()=>onCategorySelect(category),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-4 py-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-shrink-0\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                        className: \"fas fa-layer-group text-blue-500 text-base\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                        lineNumber: 611,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                    lineNumber: 610,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"min-w-0 flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs font-bold text-gray-900 truncate\",\n                                                                            children: category.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                            lineNumber: 614,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        category.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500 truncate\",\n                                                                            children: category.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                            lineNumber: 616,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                    lineNumber: 613,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                            lineNumber: 609,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 608,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-4 py-3\",\n                                                        children: category.parentId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: ((_categories_find = categories.find((c)=>c.id === category.parentId)) === null || _categories_find === void 0 ? void 0 : _categories_find.name) || 'Unknown'\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                            lineNumber: 623,\n                                                            columnNumber: 29\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-400 italic\",\n                                                            children: \"Root Category\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                            lineNumber: 627,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 621,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-4 py-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-2 py-1 rounded-none text-xs font-medium bg-blue-100 text-blue-800\",\n                                                            children: [\n                                                                ((_category__count = category._count) === null || _category__count === void 0 ? void 0 : _category__count.services) || 0,\n                                                                \" services\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                            lineNumber: 631,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 630,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-4 py-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-2 py-1 rounded-none text-xs font-medium \".concat(category.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                                            children: category.isActive ? 'Active' : 'Inactive'\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                            lineNumber: 636,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 635,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-4 py-3 text-right\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-end space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        handleEdit(category);\n                                                                    },\n                                                                    className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                                                    title: \"Edit Category\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                        lineNumber: 654,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                    lineNumber: 646,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        handleToggleActive(category);\n                                                                    },\n                                                                    className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white \".concat(category.isActive ? 'bg-orange-600 hover:bg-orange-700 focus:ring-orange-500' : 'bg-green-600 hover:bg-green-700 focus:ring-green-500', \" focus:outline-none focus:ring-2 focus:ring-offset-2\"),\n                                                                    title: category.isActive ? 'Deactivate Category' : 'Activate Category',\n                                                                    children: category.isActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                        lineNumber: 669,\n                                                                        columnNumber: 33\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                        lineNumber: 671,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                    lineNumber: 656,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        handleDelete(category);\n                                                                    },\n                                                                    className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                                                                    title: \"Delete Category\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                        lineNumber: 682,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                    lineNumber: 674,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                            lineNumber: 645,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 644,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, category.id, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 599,\n                                                columnNumber: 23\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 597,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 577,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 576,\n                            columnNumber: 15\n                        }, this),\n                        viewMode === 'list' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 border border-gray-200 rounded-none px-4 py-3 mb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-6\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 698,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-6\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 699,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Category\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 701,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 700,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-32\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Parent\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 704,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 703,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Services\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 707,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 706,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 710,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 709,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-32\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 713,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 712,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 697,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 696,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: filteredCategories.map((category)=>renderCategory(category))\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 719,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 694,\n                            columnNumber: 15\n                        }, this),\n                        viewMode === 'grid' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4\",\n                            children: getAllCategories(filteredCategories).map((category)=>renderCategoryCard(category, false))\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 726,\n                            columnNumber: 15\n                        }, this),\n                        viewMode === 'card' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3\",\n                            children: getAllCategories(filteredCategories).map((category)=>renderCategoryCard(category, true))\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 732,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 574,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                lineNumber: 550,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.AnimatePresence, {\n                children: isFormOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                    onClick: ()=>setIsFormOpen(false),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                        initial: {\n                            scale: 0.95,\n                            opacity: 0\n                        },\n                        animate: {\n                            scale: 1,\n                            opacity: 1\n                        },\n                        exit: {\n                            scale: 0.95,\n                            opacity: 0\n                        },\n                        className: \"bg-white rounded-lg p-6 w-full max-w-md mx-4\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-4\",\n                                children: editingCategory ? 'Edit Category' : 'Add Category'\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 757,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: async (e)=>{\n                                    e.preventDefault();\n                                    try {\n                                        const url = editingCategory ? \"/api/admin/categories/\".concat(editingCategory.id) : '/api/admin/categories';\n                                        const method = editingCategory ? 'PUT' : 'POST';\n                                        const response = await fetch(url, {\n                                            method,\n                                            headers: {\n                                                'Content-Type': 'application/json'\n                                            },\n                                            body: JSON.stringify({\n                                                categname: formData.name,\n                                                categdesc: formData.description,\n                                                parentid: formData.parentId ? Number(formData.parentId) : 0,\n                                                isactive: formData.isActive,\n                                                displayorder: formData.displayOrder\n                                            })\n                                        });\n                                        if (response.ok) {\n                                            setIsFormOpen(false);\n                                            fetchCategories();\n                                        } else {\n                                            const errorData = await response.json();\n                                            alert(errorData.message || 'Failed to save category');\n                                        }\n                                    } catch (error) {\n                                        console.error('Error saving category:', error);\n                                        alert('An error occurred while saving the category');\n                                    }\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Name *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 797,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        required: true,\n                                                        value: formData.name,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                name: e.target.value\n                                                            }),\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 800,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 796,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Description\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 810,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        value: formData.description,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                description: e.target.value\n                                                            }),\n                                                        rows: 3,\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 813,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 809,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Parent Category\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 822,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.parentId,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                parentId: e.target.value\n                                                            }),\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"No parent (root category)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                lineNumber: 830,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            categories.map((cat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: cat.id,\n                                                                    children: cat.name\n                                                                }, cat.id, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                    lineNumber: 832,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 825,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 821,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: formData.isActive,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    isActive: e.target.checked\n                                                                }),\n                                                            className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                            lineNumber: 841,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 text-sm text-gray-700\",\n                                                            children: \"Active\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                            lineNumber: 847,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 840,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 839,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Display Order\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 852,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: formData.displayOrder,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                displayOrder: Number(e.target.value)\n                                                            }),\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 855,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 851,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 795,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end space-x-3 mt-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>setIsFormOpen(false),\n                                                className: \"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors\",\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 865,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"submit\",\n                                                className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                                children: editingCategory ? 'Update' : 'Create'\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 872,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 864,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 761,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                        lineNumber: 750,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 743,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                lineNumber: 741,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n        lineNumber: 527,\n        columnNumber: 5\n    }, this);\n}\n_s(CategoryManagement, \"Tq8luWYvmcK5f6HCbPVBjHxoERU=\");\n_c = CategoryManagement;\nvar _c;\n$RefreshReg$(_c, \"CategoryManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/services/category-management.tsx\n"));

/***/ })

});