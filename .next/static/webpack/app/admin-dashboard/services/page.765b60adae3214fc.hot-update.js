"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/services/page",{

/***/ "(app-pages-browser)/./src/components/admin/services/option-features-management.tsx":
/*!**********************************************************************!*\
  !*** ./src/components/admin/services/option-features-management.tsx ***!
  \**********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OptionFeaturesManagement: () => (/* binding */ OptionFeaturesManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,StarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,StarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,StarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,StarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,StarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,StarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,StarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,StarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* __next_internal_client_entry_do_not_use__ OptionFeaturesManagement auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction OptionFeaturesManagement(param) {\n    let { option } = param;\n    _s();\n    const [features, setFeatures] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredFeatures, setFilteredFeatures] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isFormOpen, setIsFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingFeature, setEditingFeature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [debouncedSearchTerm, setDebouncedSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('list');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        description: '',\n        isIncluded: true,\n        isHighlighted: false,\n        displayOrder: 0\n    });\n    // Debounce search term\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OptionFeaturesManagement.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"OptionFeaturesManagement.useEffect.timer\": ()=>{\n                    setDebouncedSearchTerm(searchTerm);\n                }\n            }[\"OptionFeaturesManagement.useEffect.timer\"], 300);\n            return ({\n                \"OptionFeaturesManagement.useEffect\": ()=>clearTimeout(timer)\n            })[\"OptionFeaturesManagement.useEffect\"];\n        }\n    }[\"OptionFeaturesManagement.useEffect\"], [\n        searchTerm\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OptionFeaturesManagement.useEffect\": ()=>{\n            fetchFeatures();\n        }\n    }[\"OptionFeaturesManagement.useEffect\"], [\n        option.id\n    ]);\n    // Mock data for demonstration\n    const fetchFeatures = async ()=>{\n        setLoading(true);\n        try {\n            // Mock data - replace with actual API call\n            const mockFeatures = [\n                {\n                    id: '1',\n                    optionId: option.id,\n                    name: 'Responsive Design',\n                    description: 'Mobile-friendly design that works on all devices',\n                    isIncluded: true,\n                    isHighlighted: true,\n                    displayOrder: 1,\n                    createdAt: '2024-01-15T10:00:00Z',\n                    updatedAt: '2024-01-15T10:00:00Z',\n                    option: {\n                        id: option.id,\n                        name: option.name\n                    }\n                },\n                {\n                    id: '2',\n                    optionId: option.id,\n                    name: 'SEO Optimization',\n                    description: 'Search engine optimization for better visibility',\n                    isIncluded: true,\n                    isHighlighted: false,\n                    displayOrder: 2,\n                    createdAt: '2024-01-14T10:00:00Z',\n                    updatedAt: '2024-01-14T10:00:00Z',\n                    option: {\n                        id: option.id,\n                        name: option.name\n                    }\n                },\n                {\n                    id: '3',\n                    optionId: option.id,\n                    name: 'Analytics Integration',\n                    description: 'Google Analytics and tracking setup',\n                    isIncluded: true,\n                    isHighlighted: false,\n                    displayOrder: 3,\n                    createdAt: '2024-01-13T10:00:00Z',\n                    updatedAt: '2024-01-13T10:00:00Z',\n                    option: {\n                        id: option.id,\n                        name: option.name\n                    }\n                },\n                {\n                    id: '4',\n                    optionId: option.id,\n                    name: 'Premium Support',\n                    description: '24/7 priority customer support',\n                    isIncluded: false,\n                    isHighlighted: true,\n                    displayOrder: 4,\n                    createdAt: '2024-01-12T10:00:00Z',\n                    updatedAt: '2024-01-12T10:00:00Z',\n                    option: {\n                        id: option.id,\n                        name: option.name\n                    }\n                }\n            ];\n            setFeatures(mockFeatures);\n            setFilteredFeatures(mockFeatures);\n        } catch (error) {\n            console.error('Error fetching features:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleCreateFeature = ()=>{\n        setIsFormOpen(true);\n        setEditingFeature(null);\n        setFormData({\n            name: '',\n            description: '',\n            isIncluded: true,\n            isHighlighted: false,\n            displayOrder: 0\n        });\n    };\n    const handleEditFeature = (feature)=>{\n        setEditingFeature(feature);\n        setFormData({\n            name: feature.name,\n            description: feature.description || '',\n            isIncluded: feature.isIncluded,\n            isHighlighted: feature.isHighlighted,\n            displayOrder: feature.displayOrder\n        });\n        setIsFormOpen(true);\n    };\n    const handleDeleteFeature = async (featureId)=>{\n        if (confirm('Are you sure you want to delete this feature?')) {\n            try {\n                // Mock delete - replace with actual API call\n                setFeatures((prev)=>prev.filter((feature)=>feature.id !== featureId));\n                setFilteredFeatures((prev)=>prev.filter((feature)=>feature.id !== featureId));\n            } catch (error) {\n                console.error('Error deleting feature:', error);\n            }\n        }\n    };\n    const handleSubmitForm = async (e)=>{\n        e.preventDefault();\n        try {\n            if (editingFeature) {\n                // Update existing feature\n                const updatedFeature = {\n                    ...editingFeature,\n                    ...formData\n                };\n                setFeatures((prev)=>prev.map((feature)=>feature.id === editingFeature.id ? updatedFeature : feature));\n                setFilteredFeatures((prev)=>prev.map((feature)=>feature.id === editingFeature.id ? updatedFeature : feature));\n            } else {\n                // Create new feature\n                const newFeature = {\n                    id: Date.now().toString(),\n                    optionId: option.id,\n                    ...formData,\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString(),\n                    option: {\n                        id: option.id,\n                        name: option.name\n                    }\n                };\n                setFeatures((prev)=>[\n                        ...prev,\n                        newFeature\n                    ]);\n                setFilteredFeatures((prev)=>[\n                        ...prev,\n                        newFeature\n                    ]);\n            }\n            setIsFormOpen(false);\n            setEditingFeature(null);\n        } catch (error) {\n            console.error('Error saving feature:', error);\n        }\n    };\n    const toggleFeatureIncluded = async (featureId)=>{\n        try {\n            setFeatures((prev)=>prev.map((feature)=>feature.id === featureId ? {\n                        ...feature,\n                        isIncluded: !feature.isIncluded\n                    } : feature));\n            setFilteredFeatures((prev)=>prev.map((feature)=>feature.id === featureId ? {\n                        ...feature,\n                        isIncluded: !feature.isIncluded\n                    } : feature));\n        } catch (error) {\n            console.error('Error toggling feature:', error);\n        }\n    };\n    const toggleFeatureHighlighted = async (featureId)=>{\n        try {\n            setFeatures((prev)=>prev.map((feature)=>feature.id === featureId ? {\n                        ...feature,\n                        isHighlighted: !feature.isHighlighted\n                    } : feature));\n            setFilteredFeatures((prev)=>prev.map((feature)=>feature.id === featureId ? {\n                        ...feature,\n                        isHighlighted: !feature.isHighlighted\n                    } : feature));\n        } catch (error) {\n            console.error('Error toggling feature highlight:', error);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-200 rounded w-1/4 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            ...Array(5)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-12 bg-gray-200 rounded\"\n                            }, i, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                lineNumber: 265,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n            lineNumber: 264,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900\",\n                                children: \"Option Features\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    \"Manage features for \",\n                                    option.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleCreateFeature,\n                        className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, this),\n                            \"Add Feature\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                lineNumber: 280,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 flex flex-col sm:flex-row gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"Search features...\",\n                                value: searchTerm,\n                                onChange: (e)=>setSearchTerm(e.target.value),\n                                className: \"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{},\n                        className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 11\n                            }, this),\n                            \"Filters\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                lineNumber: 295,\n                columnNumber: 7\n            }, this),\n            viewMode === 'list' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 border border-gray-200 rounded-none px-4 py-3 mb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Feature\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Type\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-40\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1\",\n                        children: filteredFeatures.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 10\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                className: \"flex items-center py-2 px-4 rounded-none transition-all duration-200 border border-gray-200 \".concat(feature.isHighlighted ? 'bg-purple-50 border-purple-300' : feature.isIncluded ? 'bg-green-50 border-green-300' : 'bg-white hover:bg-gray-50'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-star text-base \".concat(feature.isHighlighted ? 'text-purple-500' : 'text-gray-400')\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-bold text-sm truncate \".concat(feature.isHighlighted ? 'text-purple-900' : feature.isIncluded ? 'text-green-900' : 'text-gray-900'),\n                                                children: feature.name\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 19\n                                            }, this),\n                                            feature.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs truncate \".concat(feature.isHighlighted ? 'text-purple-600' : feature.isIncluded ? 'text-green-600' : 'text-gray-600'),\n                                                children: feature.description\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2 py-1 rounded-none text-xs font-medium \".concat(feature.isIncluded ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                            children: feature.isIncluded ? 'Included' : 'Not Included'\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-20\",\n                                        children: feature.isHighlighted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2 py-1 rounded-none text-xs font-medium bg-purple-100 text-purple-800\",\n                                            children: \"Featured\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 w-40\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>toggleFeatureIncluded(feature.id),\n                                                className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white \".concat(feature.isIncluded ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500' : 'bg-green-600 hover:bg-green-700 focus:ring-green-500', \" focus:outline-none focus:ring-2 focus:ring-offset-2\"),\n                                                title: feature.isIncluded ? 'Mark as not included' : 'Mark as included',\n                                                children: feature.isIncluded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>toggleFeatureHighlighted(feature.id),\n                                                className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white \".concat(feature.isHighlighted ? 'bg-gray-600 hover:bg-gray-700 focus:ring-gray-500' : 'bg-purple-600 hover:bg-purple-700 focus:ring-purple-500', \" focus:outline-none focus:ring-2 focus:ring-offset-2\"),\n                                                title: feature.isHighlighted ? 'Remove highlight' : 'Highlight feature',\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleEditFeature(feature),\n                                                className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                                title: \"Edit Feature\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleDeleteFeature(feature.id),\n                                                className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                                                title: \"Delete Feature\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, feature.id, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                lineNumber: 317,\n                columnNumber: 9\n            }, this),\n            viewMode === 'table' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-none border border-gray-200 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"min-w-full divide-y divide-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Feature\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Type\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 457,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-right text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 450,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                            lineNumber: 449,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"bg-white divide-y divide-gray-200\",\n                            children: filteredFeatures.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.tr, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 10\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    className: \"transition-all duration-200 hover:bg-gray-50 \".concat(feature.isHighlighted ? 'bg-purple-50 border-l-4 border-l-purple-500' : feature.isIncluded ? 'bg-green-50 border-l-4 border-l-green-500' : 'bg-gray-50'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fas fa-star text-base \".concat(feature.isHighlighted ? 'text-purple-500' : 'text-gray-400')\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"min-w-0 flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs font-bold text-gray-900 truncate\",\n                                                                children: feature.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                                lineNumber: 487,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            feature.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 truncate\",\n                                                                children: feature.description\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                                lineNumber: 489,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 486,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-2 py-1 rounded-none text-xs font-medium \".concat(feature.isIncluded ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                                children: feature.isIncluded ? 'Included' : 'Not Included'\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-3\",\n                                            children: feature.isHighlighted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-2 py-1 rounded-none text-xs font-medium bg-purple-100 text-purple-800\",\n                                                children: \"Featured\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 505,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-3 text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-end space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>toggleFeatureIncluded(feature.id),\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white \".concat(feature.isIncluded ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500' : 'bg-green-600 hover:bg-green-700 focus:ring-green-500', \" focus:outline-none focus:ring-2 focus:ring-offset-2\"),\n                                                        title: feature.isIncluded ? 'Mark as not included' : 'Mark as included',\n                                                        children: feature.isIncluded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                            lineNumber: 522,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                            lineNumber: 524,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 512,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>toggleFeatureHighlighted(feature.id),\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white \".concat(feature.isHighlighted ? 'bg-gray-600 hover:bg-gray-700 focus:ring-gray-500' : 'bg-purple-600 hover:bg-purple-700 focus:ring-purple-500', \" focus:outline-none focus:ring-2 focus:ring-offset-2\"),\n                                                        title: feature.isHighlighted ? 'Remove highlight' : 'Highlight feature',\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                            lineNumber: 536,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleEditFeature(feature),\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                                        title: \"Edit Feature\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleDeleteFeature(feature.id),\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                                                        title: \"Delete Feature\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                            lineNumber: 550,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 511,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                            lineNumber: 510,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, feature.id, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                    lineNumber: 467,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                            lineNumber: 465,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                    lineNumber: 448,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                lineNumber: 447,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.AnimatePresence, {\n                children: isFormOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                    onClick: ()=>setIsFormOpen(false),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            scale: 0.95,\n                            opacity: 0\n                        },\n                        animate: {\n                            scale: 1,\n                            opacity: 1\n                        },\n                        exit: {\n                            scale: 0.95,\n                            opacity: 0\n                        },\n                        className: \"bg-white rounded-none p-6 w-full max-w-md mx-4\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: editingFeature ? 'Edit Feature' : 'Create Feature'\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 578,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmitForm,\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Name\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 583,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: formData.name,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        name: e.target.value\n                                                    }),\n                                                className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 584,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 582,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Description\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 593,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: formData.description,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        description: e.target.value\n                                                    }),\n                                                className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500\",\n                                                rows: 3\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 594,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 592,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: formData.isIncluded,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                isIncluded: e.target.checked\n                                                            }),\n                                                        className: \"h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 603,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"ml-2 block text-sm text-gray-900\",\n                                                        children: \"Included\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 609,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 602,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: formData.isHighlighted,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                isHighlighted: e.target.checked\n                                                            }),\n                                                        className: \"h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 612,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"ml-2 block text-sm text-gray-900\",\n                                                        children: \"Highlighted\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 618,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 611,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 601,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>setIsFormOpen(false),\n                                                className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50\",\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 622,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"submit\",\n                                                className: \"px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-md hover:bg-purple-700\",\n                                                children: editingFeature ? 'Update' : 'Create'\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 629,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 621,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 581,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 571,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                    lineNumber: 564,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                lineNumber: 562,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n        lineNumber: 278,\n        columnNumber: 5\n    }, this);\n}\n_s(OptionFeaturesManagement, \"+/CmXx/1GB1WO+DfqCKONQSdSds=\");\n_c = OptionFeaturesManagement;\nvar _c;\n$RefreshReg$(_c, \"OptionFeaturesManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/services/option-features-management.tsx\n"));

/***/ })

});