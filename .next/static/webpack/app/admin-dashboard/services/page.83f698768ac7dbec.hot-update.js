"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/services/page",{

/***/ "(app-pages-browser)/./src/components/admin/services/category-management.tsx":
/*!***************************************************************!*\
  !*** ./src/components/admin/services/category-management.tsx ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CategoryManagement: () => (/* binding */ CategoryManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_FolderOpenIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,FolderOpenIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_FolderOpenIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,FolderOpenIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_FolderOpenIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,FolderOpenIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FolderOpenIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_FolderOpenIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,FolderOpenIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FolderIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_FolderOpenIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,FolderOpenIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_FolderOpenIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,FolderOpenIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeSlashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_FolderOpenIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,FolderOpenIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_FolderOpenIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,FolderOpenIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_FolderOpenIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,EyeIcon,EyeSlashIcon,FolderIcon,FolderOpenIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _category_header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./category-header */ \"(app-pages-browser)/./src/components/admin/services/category-header.tsx\");\n/* __next_internal_client_entry_do_not_use__ CategoryManagement auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction CategoryManagement(param) {\n    let { selectedCategory, onCategorySelect } = param;\n    var _this = this;\n    _s();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredCategories, setFilteredCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isFormOpen, setIsFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingCategory, setEditingCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [expandedCategories, setExpandedCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentFilters, setCurrentFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('list');\n    const [density, setDensity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('comfortable');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        description: '',\n        parentId: '',\n        isActive: true,\n        displayOrder: 0\n    });\n    const filters = [\n        {\n            key: 'status',\n            label: 'Status',\n            type: 'select',\n            options: [\n                {\n                    value: '',\n                    label: 'All Status'\n                },\n                {\n                    value: 'active',\n                    label: 'Active'\n                },\n                {\n                    value: 'inactive',\n                    label: 'Inactive'\n                }\n            ]\n        },\n        {\n            key: 'parent',\n            label: 'Parent Category',\n            type: 'select',\n            options: [\n                {\n                    value: '',\n                    label: 'All Categories'\n                },\n                {\n                    value: 'root',\n                    label: 'Root Categories'\n                },\n                {\n                    value: 'sub',\n                    label: 'Sub Categories'\n                }\n            ]\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CategoryManagement.useEffect\": ()=>{\n            fetchCategories();\n        }\n    }[\"CategoryManagement.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CategoryManagement.useEffect\": ()=>{\n            filterAndSortCategories();\n        }\n    }[\"CategoryManagement.useEffect\"], [\n        categories,\n        searchQuery,\n        currentFilters\n    ]);\n    const filterAndSortCategories = ()=>{\n        let filtered = [\n            ...categories\n        ];\n        // Apply search filter\n        if (searchQuery.trim()) {\n            const searchLower = searchQuery.toLowerCase();\n            filtered = filtered.filter((category)=>category.name.toLowerCase().includes(searchLower) || category.description && category.description.toLowerCase().includes(searchLower));\n        }\n        // Apply status filter\n        if (currentFilters.status) {\n            if (currentFilters.status === 'active') {\n                filtered = filtered.filter((category)=>category.isActive);\n            } else if (currentFilters.status === 'inactive') {\n                filtered = filtered.filter((category)=>!category.isActive);\n            }\n        }\n        // Apply parent filter\n        if (currentFilters.parent) {\n            if (currentFilters.parent === 'root') {\n                filtered = filtered.filter((category)=>!category.parentId);\n            } else if (currentFilters.parent === 'sub') {\n                filtered = filtered.filter((category)=>category.parentId);\n            }\n        }\n        setFilteredCategories(filtered);\n    };\n    const buildCategoryTree = (flatCategories)=>{\n        const categoryMap = new Map();\n        const rootCategories = [];\n        // Transform and create map\n        flatCategories.forEach((cat)=>{\n            const category = {\n                id: String(cat.id),\n                name: cat.categname || cat.name,\n                description: cat.categdesc || cat.description,\n                parentId: cat.parentid ? String(cat.parentid) : undefined,\n                isActive: cat.isactive,\n                displayOrder: cat.displayorder || 0,\n                children: [],\n                _count: cat._count\n            };\n            categoryMap.set(category.id, category);\n        });\n        // Build tree structure\n        categoryMap.forEach((category)=>{\n            if (category.parentId && categoryMap.has(category.parentId)) {\n                categoryMap.get(category.parentId).children.push(category);\n            } else {\n                rootCategories.push(category);\n            }\n        });\n        // Sort by display order\n        const sortCategories = (cats)=>{\n            cats.sort((a, b)=>a.displayOrder - b.displayOrder);\n            cats.forEach((cat)=>{\n                if (cat.children) {\n                    sortCategories(cat.children);\n                }\n            });\n        };\n        sortCategories(rootCategories);\n        return rootCategories;\n    };\n    const fetchCategories = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch('/api/admin/categories?limit=100');\n            if (response.ok) {\n                const data = await response.json();\n                const categoriesData = data.data || data.categories || [];\n                setCategories(buildCategoryTree(categoriesData));\n            } else {\n                console.error('Failed to fetch categories:', response.status, response.statusText);\n                setCategories([]);\n            }\n        } catch (error) {\n            console.error('Error fetching categories:', error);\n            setCategories([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleEdit = (category)=>{\n        setEditingCategory(category);\n        setFormData({\n            name: category.name,\n            description: category.description || '',\n            parentId: category.parentId || '',\n            isActive: category.isActive,\n            displayOrder: category.displayOrder\n        });\n        setIsFormOpen(true);\n    };\n    const handleDelete = async (category)=>{\n        if (!confirm('Are you sure you want to delete \"'.concat(category.name, '\"?'))) return;\n        try {\n            const response = await fetch(\"/api/admin/categories/\".concat(category.id), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                await fetchCategories();\n                if ((selectedCategory === null || selectedCategory === void 0 ? void 0 : selectedCategory.id) === category.id) {\n                    onCategorySelect(null);\n                }\n            } else {\n                const errorData = await response.json();\n                alert(errorData.message || 'Failed to delete category');\n            }\n        } catch (error) {\n            console.error('Error deleting category:', error);\n            alert('An error occurred while deleting the category');\n        }\n    };\n    const handleToggleActive = async (category)=>{\n        try {\n            const response = await fetch(\"/api/admin/categories/\".concat(category.id), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    categname: category.name,\n                    categdesc: category.description,\n                    parentid: category.parentId ? Number(category.parentId) : 0,\n                    isactive: !category.isActive,\n                    displayorder: category.displayOrder\n                })\n            });\n            if (response.ok) {\n                fetchCategories();\n            }\n        } catch (error) {\n            console.error('Error toggling category status:', error);\n        }\n    };\n    const toggleExpanded = (categoryId)=>{\n        const newExpanded = new Set(expandedCategories);\n        if (newExpanded.has(categoryId)) {\n            newExpanded.delete(categoryId);\n        } else {\n            newExpanded.add(categoryId);\n        }\n        setExpandedCategories(newExpanded);\n    };\n    const renderCategory = function(category) {\n        let level = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        var _category_children;\n        const isExpanded = expandedCategories.has(category.id);\n        const hasChildren = category.children && category.children.length > 0;\n        const isSelected = (selectedCategory === null || selectedCategory === void 0 ? void 0 : selectedCategory.id) === category.id;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"select-none\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2 py-2 px-3 rounded-none cursor-pointer transition-colors \".concat(isSelected ? 'bg-blue-50 border border-blue-200' : 'hover:bg-gray-100'),\n                    style: {\n                        marginLeft: \"\".concat(level * 20, \"px\")\n                    },\n                    onClick: ()=>onCategorySelect(category),\n                    children: [\n                        hasChildren ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: (e)=>{\n                                e.stopPropagation();\n                                toggleExpanded(category.id);\n                            },\n                            className: \"p-1 hover:bg-gray-200 rounded-none\",\n                            children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_FolderOpenIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"h-4 w-4 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 17\n                            }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_FolderOpenIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 17\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 13\n                        }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-6\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 13\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 flex-1\",\n                            children: [\n                                hasChildren ? isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_FolderOpenIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-5 w-5 text-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 17\n                                }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_FolderOpenIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-5 w-5 text-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 17\n                                }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_FolderOpenIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-5 w-5 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 15\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-bold text-lg \".concat(isSelected ? 'text-blue-900' : 'text-gray-900'),\n                                                        children: category.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 19\n                                                    }, _this),\n                                                    category.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base \".concat(isSelected ? 'text-blue-600' : 'text-gray-600'),\n                                                        children: category.description\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 21\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 17\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1\",\n                                                children: [\n                                                    category._count && typeof category._count.services === 'number' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"bg-blue-100 text-blue-800 px-3 py-1 rounded-none text-base font-bold\",\n                                                        children: [\n                                                            category._count.services,\n                                                            \" services\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 21\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            handleEdit(category);\n                                                        },\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                                        title: \"Edit category\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_FolderOpenIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                            lineNumber: 331,\n                                                            columnNumber: 21\n                                                        }, _this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 19\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            handleToggleActive(category);\n                                                        },\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white \".concat(category.isActive ? 'bg-orange-600 hover:bg-orange-700 focus:ring-orange-500' : 'bg-green-600 hover:bg-green-700 focus:ring-green-500', \" focus:outline-none focus:ring-2 focus:ring-offset-2\"),\n                                                        title: category.isActive ? 'Deactivate category' : 'Activate category',\n                                                        children: category.isActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_FolderOpenIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 23\n                                                        }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_FolderOpenIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 23\n                                                        }, _this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 19\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            handleDelete(category);\n                                                        },\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                                                        title: \"Delete category\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_FolderOpenIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 21\n                                                        }, _this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 19\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 17\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 15\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 11\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 265,\n                    columnNumber: 9\n                }, _this),\n                hasChildren && isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"ml-6\",\n                    children: (_category_children = category.children) === null || _category_children === void 0 ? void 0 : _category_children.map((child)=>renderCategory(child, level + 1))\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 370,\n                    columnNumber: 11\n                }, _this)\n            ]\n        }, category.id, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n            lineNumber: 264,\n            columnNumber: 7\n        }, _this);\n    };\n    const handleCreateClick = ()=>{\n        setIsFormOpen(true);\n        setEditingCategory(null);\n        setFormData({\n            name: '',\n            description: '',\n            parentId: '',\n            isActive: true,\n            displayOrder: 0\n        });\n    };\n    const handleFiltersChange = (newFilters)=>{\n        setCurrentFilters(newFilters);\n    };\n    const renderCategoryCard = function(category) {\n        let isLargeCard = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        var _category_children;\n        const isSelected = (selectedCategory === null || selectedCategory === void 0 ? void 0 : selectedCategory.id) === category.id;\n        const hasChildren = category.children && category.children.length > 0;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-50 border rounded-none cursor-pointer transition-all duration-200 \".concat(isSelected ? 'border-blue-500 shadow-md bg-blue-50' : 'border-gray-200 hover:border-gray-300 hover:shadow-sm hover:bg-gray-100', \" \").concat(isLargeCard ? 'p-6' : 'p-4'),\n            onClick: ()=>onCategorySelect(category),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 mb-2\",\n                                children: [\n                                    hasChildren ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_FolderOpenIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-5 w-5 text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 17\n                                    }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_FolderOpenIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-5 w-5 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 17\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold \".concat(isSelected ? 'text-blue-900' : 'text-gray-900'),\n                                        children: category.name\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 15\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(category.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'),\n                                        children: category.isActive ? 'Active' : 'Inactive'\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 15\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 13\n                            }, _this),\n                            category.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm mb-3 \".concat(isSelected ? 'text-blue-600' : 'text-gray-600'),\n                                children: category.description\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 15\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 text-xs text-gray-500\",\n                                children: [\n                                    category._count && typeof category._count.services === 'number' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            category._count.services,\n                                            \" services\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 17\n                                    }, _this),\n                                    hasChildren && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            (_category_children = category.children) === null || _category_children === void 0 ? void 0 : _category_children.length,\n                                            \" subcategories\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 17\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Order: \",\n                                            category.displayOrder\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 15\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 13\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                        lineNumber: 409,\n                        columnNumber: 11\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-1 ml-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: (e)=>{\n                                    e.stopPropagation();\n                                    handleEdit(category);\n                                },\n                                className: \"text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded p-1\",\n                                title: \"Edit category\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_FolderOpenIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 454,\n                                    columnNumber: 15\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 13\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: (e)=>{\n                                    e.stopPropagation();\n                                    handleToggleActive(category);\n                                },\n                                className: \"rounded transition-colors p-1 \".concat(category.isActive ? 'text-green-600 hover:text-green-700 hover:bg-green-50' : 'text-gray-400 hover:text-gray-600 hover:bg-gray-50'),\n                                title: category.isActive ? 'Deactivate category' : 'Activate category',\n                                children: category.isActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_FolderOpenIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 470,\n                                    columnNumber: 17\n                                }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_FolderOpenIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 17\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 13\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: (e)=>{\n                                    e.stopPropagation();\n                                    handleDelete(category);\n                                },\n                                className: \"text-gray-400 hover:text-red-600 hover:bg-red-50 rounded p-1\",\n                                title: \"Delete category\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_FolderOpenIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 15\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 476,\n                                columnNumber: 13\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                        lineNumber: 445,\n                        columnNumber: 11\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                lineNumber: 408,\n                columnNumber: 9\n            }, _this)\n        }, category.id, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n            lineNumber: 399,\n            columnNumber: 7\n        }, _this);\n    };\n    const getAllCategories = (cats)=>{\n        let all = [];\n        cats.forEach((cat)=>{\n            all.push(cat);\n            if (cat.children && cat.children.length > 0) {\n                all = all.concat(getAllCategories(cat.children));\n            }\n        });\n        return all;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_category_header__WEBPACK_IMPORTED_MODULE_2__.CategoryHeader, {\n                title: \"Categories\",\n                description: \"Manage service categories and subcategories\",\n                searchPlaceholder: \"Search categories by name or description...\",\n                searchQuery: searchQuery,\n                onSearchChange: setSearchQuery,\n                enableSearch: true,\n                enableFilters: true,\n                enableViewControls: true,\n                enableCreate: true,\n                onCreateClick: handleCreateClick,\n                createButtonText: \"Add Category\",\n                viewMode: viewMode,\n                onViewModeChange: setViewMode,\n                filters: filters,\n                onFiltersChange: handleFiltersChange,\n                currentFilters: currentFilters,\n                itemCount: filteredCategories.length,\n                totalItems: categories.length\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                lineNumber: 505,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm border border-gray-200\",\n                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 530,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-gray-600\",\n                            children: \"Loading categories...\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 531,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 529,\n                    columnNumber: 11\n                }, this) : filteredCategories.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_FolderOpenIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 535,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                            children: \"No categories found\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 536,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: searchQuery || Object.keys(currentFilters).some((key)=>currentFilters[key]) ? 'Try adjusting your search terms or filters.' : 'Get started by creating your first category.'\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 537,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleCreateClick,\n                            className: \"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_FolderOpenIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                    lineNumber: 546,\n                                    columnNumber: 15\n                                }, this),\n                                \"Add Category\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 542,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 534,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4\",\n                    children: [\n                        viewMode === 'table' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-none border border-gray-200 overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"min-w-full divide-y divide-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        className: \"bg-gray-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Category\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 557,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Parent\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 560,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Services\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 563,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-4 py-3 text-right text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                                    children: \"Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 569,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                            lineNumber: 556,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 555,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        className: \"bg-white divide-y divide-gray-200\",\n                                        children: getAllCategories(filteredCategories).map((category)=>{\n                                            var _categories_find, _category__count;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.tr, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 10\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                className: \"cursor-pointer transition-all duration-200 hover:bg-gray-50 \".concat((selectedCategory === null || selectedCategory === void 0 ? void 0 : selectedCategory.id) === category.id ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''),\n                                                onClick: ()=>onCategorySelect(category),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-4 py-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-shrink-0\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_FolderOpenIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                        lineNumber: 588,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                    lineNumber: 587,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"min-w-0 flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-bold text-gray-900 truncate\",\n                                                                            children: category.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                            lineNumber: 591,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        category.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500 truncate\",\n                                                                            children: category.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                            lineNumber: 593,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                    lineNumber: 590,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                            lineNumber: 586,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 585,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-4 py-3\",\n                                                        children: category.parentId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: ((_categories_find = categories.find((c)=>c.id === category.parentId)) === null || _categories_find === void 0 ? void 0 : _categories_find.name) || 'Unknown'\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                            lineNumber: 600,\n                                                            columnNumber: 29\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-400 italic\",\n                                                            children: \"Root Category\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                            lineNumber: 604,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 598,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-4 py-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-2 py-1 rounded-none text-xs font-medium bg-blue-100 text-blue-800\",\n                                                            children: [\n                                                                ((_category__count = category._count) === null || _category__count === void 0 ? void 0 : _category__count.services) || 0,\n                                                                \" services\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                            lineNumber: 608,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 607,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-4 py-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-2 py-1 rounded-none text-xs font-medium \".concat(category.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                                            children: category.isActive ? 'Active' : 'Inactive'\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                            lineNumber: 613,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 612,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-4 py-3 text-right\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-end space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        handleEdit(category);\n                                                                    },\n                                                                    className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                                                    title: \"Edit Category\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_FolderOpenIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                        lineNumber: 631,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                    lineNumber: 623,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        handleToggleActive(category);\n                                                                    },\n                                                                    className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white \".concat(category.isActive ? 'bg-orange-600 hover:bg-orange-700 focus:ring-orange-500' : 'bg-green-600 hover:bg-green-700 focus:ring-green-500', \" focus:outline-none focus:ring-2 focus:ring-offset-2\"),\n                                                                    title: category.isActive ? 'Deactivate Category' : 'Activate Category',\n                                                                    children: category.isActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_FolderOpenIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                        lineNumber: 646,\n                                                                        columnNumber: 33\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_FolderOpenIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                        lineNumber: 648,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                    lineNumber: 633,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        handleDelete(category);\n                                                                    },\n                                                                    className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                                                                    title: \"Delete Category\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_EyeIcon_EyeSlashIcon_FolderIcon_FolderOpenIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                        lineNumber: 659,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                    lineNumber: 651,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                            lineNumber: 622,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 621,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, category.id, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 576,\n                                                columnNumber: 23\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 574,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 554,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 553,\n                            columnNumber: 15\n                        }, this),\n                        viewMode === 'list' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: filteredCategories.map((category)=>renderCategory(category))\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 671,\n                            columnNumber: 15\n                        }, this),\n                        viewMode === 'grid' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4\",\n                            children: getAllCategories(filteredCategories).map((category)=>renderCategoryCard(category, false))\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 677,\n                            columnNumber: 15\n                        }, this),\n                        viewMode === 'card' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3\",\n                            children: getAllCategories(filteredCategories).map((category)=>renderCategoryCard(category, true))\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                            lineNumber: 683,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 551,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                lineNumber: 527,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.AnimatePresence, {\n                children: isFormOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                    onClick: ()=>setIsFormOpen(false),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        initial: {\n                            scale: 0.95,\n                            opacity: 0\n                        },\n                        animate: {\n                            scale: 1,\n                            opacity: 1\n                        },\n                        exit: {\n                            scale: 0.95,\n                            opacity: 0\n                        },\n                        className: \"bg-white rounded-lg p-6 w-full max-w-md mx-4\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-4\",\n                                children: editingCategory ? 'Edit Category' : 'Add Category'\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 708,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: async (e)=>{\n                                    e.preventDefault();\n                                    try {\n                                        const url = editingCategory ? \"/api/admin/categories/\".concat(editingCategory.id) : '/api/admin/categories';\n                                        const method = editingCategory ? 'PUT' : 'POST';\n                                        const response = await fetch(url, {\n                                            method,\n                                            headers: {\n                                                'Content-Type': 'application/json'\n                                            },\n                                            body: JSON.stringify({\n                                                categname: formData.name,\n                                                categdesc: formData.description,\n                                                parentid: formData.parentId ? Number(formData.parentId) : 0,\n                                                isactive: formData.isActive,\n                                                displayorder: formData.displayOrder\n                                            })\n                                        });\n                                        if (response.ok) {\n                                            setIsFormOpen(false);\n                                            fetchCategories();\n                                        } else {\n                                            const errorData = await response.json();\n                                            alert(errorData.message || 'Failed to save category');\n                                        }\n                                    } catch (error) {\n                                        console.error('Error saving category:', error);\n                                        alert('An error occurred while saving the category');\n                                    }\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Name *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 748,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        required: true,\n                                                        value: formData.name,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                name: e.target.value\n                                                            }),\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 751,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 747,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Description\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 761,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        value: formData.description,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                description: e.target.value\n                                                            }),\n                                                        rows: 3,\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 764,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 760,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Parent Category\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 773,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.parentId,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                parentId: e.target.value\n                                                            }),\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"No parent (root category)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                lineNumber: 781,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            categories.map((cat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: cat.id,\n                                                                    children: cat.name\n                                                                }, cat.id, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                                    lineNumber: 783,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 776,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 772,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: formData.isActive,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    isActive: e.target.checked\n                                                                }),\n                                                            className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                            lineNumber: 792,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 text-sm text-gray-700\",\n                                                            children: \"Active\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                            lineNumber: 798,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                    lineNumber: 791,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 790,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Display Order\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 803,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: formData.displayOrder,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                displayOrder: Number(e.target.value)\n                                                            }),\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                        lineNumber: 806,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 802,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 746,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end space-x-3 mt-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>setIsFormOpen(false),\n                                                className: \"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors\",\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 816,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"submit\",\n                                                className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                                children: editingCategory ? 'Update' : 'Create'\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                                lineNumber: 823,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                        lineNumber: 815,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                                lineNumber: 712,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                        lineNumber: 701,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                    lineNumber: 694,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n                lineNumber: 692,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/category-management.tsx\",\n        lineNumber: 504,\n        columnNumber: 5\n    }, this);\n}\n_s(CategoryManagement, \"Tq8luWYvmcK5f6HCbPVBjHxoERU=\");\n_c = CategoryManagement;\nvar _c;\n$RefreshReg$(_c, \"CategoryManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2FkbWluL3NlcnZpY2VzL2NhdGVnb3J5LW1hbmFnZW1lbnQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUUyQztBQUNZO0FBV25CO0FBQ2M7QUE2QjNDLFNBQVNjLG1CQUFtQixLQUErRDtRQUEvRCxFQUFFQyxnQkFBZ0IsRUFBRUMsZ0JBQWdCLEVBQTJCLEdBQS9EOzs7SUFDakMsTUFBTSxDQUFDQyxZQUFZQyxjQUFjLEdBQUdsQiwrQ0FBUUEsQ0FBYSxFQUFFO0lBQzNELE1BQU0sQ0FBQ21CLG9CQUFvQkMsc0JBQXNCLEdBQUdwQiwrQ0FBUUEsQ0FBYSxFQUFFO0lBQzNFLE1BQU0sQ0FBQ3FCLFNBQVNDLFdBQVcsR0FBR3RCLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ3VCLFlBQVlDLGNBQWMsR0FBR3hCLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ3lCLGlCQUFpQkMsbUJBQW1CLEdBQUcxQiwrQ0FBUUEsQ0FBa0I7SUFDeEUsTUFBTSxDQUFDMkIsb0JBQW9CQyxzQkFBc0IsR0FBRzVCLCtDQUFRQSxDQUFjLElBQUk2QjtJQUM5RSxNQUFNLENBQUNDLGFBQWFDLGVBQWUsR0FBRy9CLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ2dDLGdCQUFnQkMsa0JBQWtCLEdBQUdqQywrQ0FBUUEsQ0FBeUIsQ0FBQztJQUM5RSxNQUFNLENBQUNrQyxVQUFVQyxZQUFZLEdBQUduQywrQ0FBUUEsQ0FBcUM7SUFDN0UsTUFBTSxDQUFDb0MsU0FBU0MsV0FBVyxHQUFHckMsK0NBQVFBLENBQXlDO0lBRS9FLE1BQU0sQ0FBQ3NDLFVBQVVDLFlBQVksR0FBR3ZDLCtDQUFRQSxDQUFtQjtRQUN6RHdDLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsY0FBYztJQUNoQjtJQUVBLE1BQU1DLFVBQVU7UUFDZDtZQUNFQyxLQUFLO1lBQ0xDLE9BQU87WUFDUEMsTUFBTTtZQUNOQyxTQUFTO2dCQUNQO29CQUFFQyxPQUFPO29CQUFJSCxPQUFPO2dCQUFhO2dCQUNqQztvQkFBRUcsT0FBTztvQkFBVUgsT0FBTztnQkFBUztnQkFDbkM7b0JBQUVHLE9BQU87b0JBQVlILE9BQU87Z0JBQVc7YUFDeEM7UUFDSDtRQUNBO1lBQ0VELEtBQUs7WUFDTEMsT0FBTztZQUNQQyxNQUFNO1lBQ05DLFNBQVM7Z0JBQ1A7b0JBQUVDLE9BQU87b0JBQUlILE9BQU87Z0JBQWlCO2dCQUNyQztvQkFBRUcsT0FBTztvQkFBUUgsT0FBTztnQkFBa0I7Z0JBQzFDO29CQUFFRyxPQUFPO29CQUFPSCxPQUFPO2dCQUFpQjthQUN6QztRQUNIO0tBQ0Q7SUFFRDlDLGdEQUFTQTt3Q0FBQztZQUNSa0Q7UUFDRjt1Q0FBRyxFQUFFO0lBRUxsRCxnREFBU0E7d0NBQUM7WUFDUm1EO1FBQ0Y7dUNBQUc7UUFBQ25DO1FBQVlhO1FBQWFFO0tBQWU7SUFFNUMsTUFBTW9CLDBCQUEwQjtRQUM5QixJQUFJQyxXQUFXO2VBQUlwQztTQUFXO1FBRTlCLHNCQUFzQjtRQUN0QixJQUFJYSxZQUFZd0IsSUFBSSxJQUFJO1lBQ3RCLE1BQU1DLGNBQWN6QixZQUFZMEIsV0FBVztZQUMzQ0gsV0FBV0EsU0FBU0ksTUFBTSxDQUFDQyxDQUFBQSxXQUN6QkEsU0FBU2xCLElBQUksQ0FBQ2dCLFdBQVcsR0FBR0csUUFBUSxDQUFDSixnQkFDcENHLFNBQVNqQixXQUFXLElBQUlpQixTQUFTakIsV0FBVyxDQUFDZSxXQUFXLEdBQUdHLFFBQVEsQ0FBQ0o7UUFFekU7UUFFQSxzQkFBc0I7UUFDdEIsSUFBSXZCLGVBQWU0QixNQUFNLEVBQUU7WUFDekIsSUFBSTVCLGVBQWU0QixNQUFNLEtBQUssVUFBVTtnQkFDdENQLFdBQVdBLFNBQVNJLE1BQU0sQ0FBQ0MsQ0FBQUEsV0FBWUEsU0FBU2YsUUFBUTtZQUMxRCxPQUFPLElBQUlYLGVBQWU0QixNQUFNLEtBQUssWUFBWTtnQkFDL0NQLFdBQVdBLFNBQVNJLE1BQU0sQ0FBQ0MsQ0FBQUEsV0FBWSxDQUFDQSxTQUFTZixRQUFRO1lBQzNEO1FBQ0Y7UUFFQSxzQkFBc0I7UUFDdEIsSUFBSVgsZUFBZTZCLE1BQU0sRUFBRTtZQUN6QixJQUFJN0IsZUFBZTZCLE1BQU0sS0FBSyxRQUFRO2dCQUNwQ1IsV0FBV0EsU0FBU0ksTUFBTSxDQUFDQyxDQUFBQSxXQUFZLENBQUNBLFNBQVNoQixRQUFRO1lBQzNELE9BQU8sSUFBSVYsZUFBZTZCLE1BQU0sS0FBSyxPQUFPO2dCQUMxQ1IsV0FBV0EsU0FBU0ksTUFBTSxDQUFDQyxDQUFBQSxXQUFZQSxTQUFTaEIsUUFBUTtZQUMxRDtRQUNGO1FBRUF0QixzQkFBc0JpQztJQUN4QjtJQUVBLE1BQU1TLG9CQUFvQixDQUFDQztRQUN6QixNQUFNQyxjQUFjLElBQUlDO1FBQ3hCLE1BQU1DLGlCQUE2QixFQUFFO1FBRXJDLDJCQUEyQjtRQUMzQkgsZUFBZUksT0FBTyxDQUFDQyxDQUFBQTtZQUNyQixNQUFNVixXQUFxQjtnQkFDekJXLElBQUlDLE9BQU9GLElBQUlDLEVBQUU7Z0JBQ2pCN0IsTUFBTTRCLElBQUlHLFNBQVMsSUFBSUgsSUFBSTVCLElBQUk7Z0JBQy9CQyxhQUFhMkIsSUFBSUksU0FBUyxJQUFJSixJQUFJM0IsV0FBVztnQkFDN0NDLFVBQVUwQixJQUFJSyxRQUFRLEdBQUdILE9BQU9GLElBQUlLLFFBQVEsSUFBSUM7Z0JBQ2hEL0IsVUFBVXlCLElBQUlPLFFBQVE7Z0JBQ3RCL0IsY0FBY3dCLElBQUlRLFlBQVksSUFBSTtnQkFDbENDLFVBQVUsRUFBRTtnQkFDWkMsUUFBUVYsSUFBSVUsTUFBTTtZQUNwQjtZQUNBZCxZQUFZZSxHQUFHLENBQUNyQixTQUFTVyxFQUFFLEVBQUVYO1FBQy9CO1FBRUEsdUJBQXVCO1FBQ3ZCTSxZQUFZRyxPQUFPLENBQUNULENBQUFBO1lBQ2xCLElBQUlBLFNBQVNoQixRQUFRLElBQUlzQixZQUFZZ0IsR0FBRyxDQUFDdEIsU0FBU2hCLFFBQVEsR0FBRztnQkFDM0RzQixZQUFZaUIsR0FBRyxDQUFDdkIsU0FBU2hCLFFBQVEsRUFBRW1DLFFBQVEsQ0FBQ0ssSUFBSSxDQUFDeEI7WUFDbkQsT0FBTztnQkFDTFEsZUFBZWdCLElBQUksQ0FBQ3hCO1lBQ3RCO1FBQ0Y7UUFFQSx3QkFBd0I7UUFDeEIsTUFBTXlCLGlCQUFpQixDQUFDQztZQUN0QkEsS0FBS0MsSUFBSSxDQUFDLENBQUNDLEdBQUdDLElBQU1ELEVBQUUxQyxZQUFZLEdBQUcyQyxFQUFFM0MsWUFBWTtZQUNuRHdDLEtBQUtqQixPQUFPLENBQUNDLENBQUFBO2dCQUNYLElBQUlBLElBQUlTLFFBQVEsRUFBRTtvQkFDaEJNLGVBQWVmLElBQUlTLFFBQVE7Z0JBQzdCO1lBQ0Y7UUFDRjtRQUVBTSxlQUFlakI7UUFDZixPQUFPQTtJQUNUO0lBRUEsTUFBTWYsa0JBQWtCO1FBQ3RCLElBQUk7WUFDRjdCLFdBQVc7WUFDWCxNQUFNa0UsV0FBVyxNQUFNQyxNQUFNO1lBRTdCLElBQUlELFNBQVNFLEVBQUUsRUFBRTtnQkFDZixNQUFNQyxPQUFPLE1BQU1ILFNBQVNJLElBQUk7Z0JBQ2hDLE1BQU1DLGlCQUFpQkYsS0FBS0EsSUFBSSxJQUFJQSxLQUFLMUUsVUFBVSxJQUFJLEVBQUU7Z0JBQ3pEQyxjQUFjNEMsa0JBQWtCK0I7WUFDbEMsT0FBTztnQkFDTEMsUUFBUUMsS0FBSyxDQUFDLCtCQUErQlAsU0FBUzVCLE1BQU0sRUFBRTRCLFNBQVNRLFVBQVU7Z0JBQ2pGOUUsY0FBYyxFQUFFO1lBQ2xCO1FBQ0YsRUFBRSxPQUFPNkUsT0FBTztZQUNkRCxRQUFRQyxLQUFLLENBQUMsOEJBQThCQTtZQUM1QzdFLGNBQWMsRUFBRTtRQUNsQixTQUFVO1lBQ1JJLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTTJFLGFBQWEsQ0FBQ3ZDO1FBQ2xCaEMsbUJBQW1CZ0M7UUFDbkJuQixZQUFZO1lBQ1ZDLE1BQU1rQixTQUFTbEIsSUFBSTtZQUNuQkMsYUFBYWlCLFNBQVNqQixXQUFXLElBQUk7WUFDckNDLFVBQVVnQixTQUFTaEIsUUFBUSxJQUFJO1lBQy9CQyxVQUFVZSxTQUFTZixRQUFRO1lBQzNCQyxjQUFjYyxTQUFTZCxZQUFZO1FBQ3JDO1FBQ0FwQixjQUFjO0lBQ2hCO0lBRUEsTUFBTTBFLGVBQWUsT0FBT3hDO1FBQzFCLElBQUksQ0FBQ3lDLFFBQVEsb0NBQWtELE9BQWR6QyxTQUFTbEIsSUFBSSxFQUFDLFFBQU07UUFFckUsSUFBSTtZQUNGLE1BQU1nRCxXQUFXLE1BQU1DLE1BQU0seUJBQXFDLE9BQVovQixTQUFTVyxFQUFFLEdBQUk7Z0JBQ25FK0IsUUFBUTtZQUNWO1lBRUEsSUFBSVosU0FBU0UsRUFBRSxFQUFFO2dCQUNmLE1BQU12QztnQkFDTixJQUFJcEMsQ0FBQUEsNkJBQUFBLHVDQUFBQSxpQkFBa0JzRCxFQUFFLE1BQUtYLFNBQVNXLEVBQUUsRUFBRTtvQkFDeENyRCxpQkFBaUI7Z0JBQ25CO1lBQ0YsT0FBTztnQkFDTCxNQUFNcUYsWUFBWSxNQUFNYixTQUFTSSxJQUFJO2dCQUNyQ1UsTUFBTUQsVUFBVUUsT0FBTyxJQUFJO1lBQzdCO1FBQ0YsRUFBRSxPQUFPUixPQUFPO1lBQ2RELFFBQVFDLEtBQUssQ0FBQyw0QkFBNEJBO1lBQzFDTyxNQUFNO1FBQ1I7SUFDRjtJQUVBLE1BQU1FLHFCQUFxQixPQUFPOUM7UUFDaEMsSUFBSTtZQUNGLE1BQU04QixXQUFXLE1BQU1DLE1BQU0seUJBQXFDLE9BQVovQixTQUFTVyxFQUFFLEdBQUk7Z0JBQ25FK0IsUUFBUTtnQkFDUkssU0FBUztvQkFBRSxnQkFBZ0I7Z0JBQW1CO2dCQUM5Q0MsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO29CQUNuQnJDLFdBQVdiLFNBQVNsQixJQUFJO29CQUN4QmdDLFdBQVdkLFNBQVNqQixXQUFXO29CQUMvQmdDLFVBQVVmLFNBQVNoQixRQUFRLEdBQUdtRSxPQUFPbkQsU0FBU2hCLFFBQVEsSUFBSTtvQkFDMURpQyxVQUFVLENBQUNqQixTQUFTZixRQUFRO29CQUM1QmlDLGNBQWNsQixTQUFTZCxZQUFZO2dCQUNyQztZQUNGO1lBQ0EsSUFBSTRDLFNBQVNFLEVBQUUsRUFBRTtnQkFDZnZDO1lBQ0Y7UUFDRixFQUFFLE9BQU80QyxPQUFPO1lBQ2RELFFBQVFDLEtBQUssQ0FBQyxtQ0FBbUNBO1FBQ25EO0lBQ0Y7SUFFQSxNQUFNZSxpQkFBaUIsQ0FBQ0M7UUFDdEIsTUFBTUMsY0FBYyxJQUFJbkYsSUFBSUY7UUFDNUIsSUFBSXFGLFlBQVloQyxHQUFHLENBQUMrQixhQUFhO1lBQy9CQyxZQUFZQyxNQUFNLENBQUNGO1FBQ3JCLE9BQU87WUFDTEMsWUFBWUUsR0FBRyxDQUFDSDtRQUNsQjtRQUNBbkYsc0JBQXNCb0Y7SUFDeEI7SUFFQSxNQUFNRyxpQkFBaUIsU0FBQ3pEO1lBQW9CMEQseUVBQWdCO1lBaUhqRDFEO1FBaEhULE1BQU0yRCxhQUFhMUYsbUJBQW1CcUQsR0FBRyxDQUFDdEIsU0FBU1csRUFBRTtRQUNyRCxNQUFNaUQsY0FBYzVELFNBQVNtQixRQUFRLElBQUluQixTQUFTbUIsUUFBUSxDQUFDMEMsTUFBTSxHQUFHO1FBQ3BFLE1BQU1DLGFBQWF6RyxDQUFBQSw2QkFBQUEsdUNBQUFBLGlCQUFrQnNELEVBQUUsTUFBS1gsU0FBU1csRUFBRTtRQUV2RCxxQkFDRSw4REFBQ29EO1lBQXNCQyxXQUFVOzs4QkFDL0IsOERBQUNEO29CQUNDQyxXQUFXLHVGQUlWLE9BSENGLGFBQ0ksc0NBQ0E7b0JBRU5HLE9BQU87d0JBQUVDLFlBQVksR0FBYyxPQUFYUixRQUFRLElBQUc7b0JBQUk7b0JBQ3ZDUyxTQUFTLElBQU03RyxpQkFBaUIwQzs7d0JBRS9CNEQsNEJBQ0MsOERBQUNROzRCQUNDRCxTQUFTLENBQUNFO2dDQUNSQSxFQUFFQyxlQUFlO2dDQUNqQmxCLGVBQWVwRCxTQUFTVyxFQUFFOzRCQUM1Qjs0QkFDQXFELFdBQVU7c0NBRVRMLDJCQUNDLDhEQUFDM0csdU1BQWVBO2dDQUFDZ0gsV0FBVTs7Ozs7c0RBRTNCLDhEQUFDakgsdU1BQWdCQTtnQ0FBQ2lILFdBQVU7Ozs7Ozs7Ozs7a0RBSWhDLDhEQUFDRDs0QkFBSUMsV0FBVTs7Ozs7O3NDQUdqQiw4REFBQ0Q7NEJBQUlDLFdBQVU7O2dDQUNaSixjQUNDRCwyQkFDRSw4REFBQzdHLHVNQUFjQTtvQ0FBQ2tILFdBQVU7Ozs7OzBEQUUxQiw4REFBQ25ILHVNQUFVQTtvQ0FBQ21ILFdBQVU7Ozs7OzBEQUd4Qiw4REFBQ25ILHVNQUFVQTtvQ0FBQ21ILFdBQVU7Ozs7Ozs4Q0FHeEIsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEOztrRUFDQyw4REFBQ1E7d0RBQUdQLFdBQVcscUJBQW9FLE9BQS9DRixhQUFhLGtCQUFrQjtrRUFDaEU5RCxTQUFTbEIsSUFBSTs7Ozs7O29EQUVma0IsU0FBU2pCLFdBQVcsa0JBQ25CLDhEQUFDeUY7d0RBQUVSLFdBQVcsYUFBNEQsT0FBL0NGLGFBQWEsa0JBQWtCO2tFQUN2RDlELFNBQVNqQixXQUFXOzs7Ozs7Ozs7Ozs7MERBSzNCLDhEQUFDZ0Y7Z0RBQUlDLFdBQVU7O29EQUNaaEUsU0FBU29CLE1BQU0sSUFBSSxPQUFPcEIsU0FBU29CLE1BQU0sQ0FBQ3FELFFBQVEsS0FBSywwQkFDdEQsOERBQUNDO3dEQUFLVixXQUFVOzs0REFDYmhFLFNBQVNvQixNQUFNLENBQUNxRCxRQUFROzREQUFDOzs7Ozs7O2tFQUk5Qiw4REFBQ0w7d0RBQ0NELFNBQVMsQ0FBQ0U7NERBQ1JBLEVBQUVDLGVBQWU7NERBQ2pCL0IsV0FBV3ZDO3dEQUNiO3dEQUNBZ0UsV0FBVTt3REFDVlcsT0FBTTtrRUFFTiw0RUFBQ2hJLHVNQUFVQTs0REFBQ3FILFdBQVU7Ozs7Ozs7Ozs7O2tFQUd4Qiw4REFBQ0k7d0RBQ0NELFNBQVMsQ0FBQ0U7NERBQ1JBLEVBQUVDLGVBQWU7NERBQ2pCeEIsbUJBQW1COUM7d0RBQ3JCO3dEQUNBZ0UsV0FBVyw0R0FJVixPQUhDaEUsU0FBU2YsUUFBUSxHQUNiLDREQUNBLHdEQUNMO3dEQUNEMEYsT0FBTzNFLFNBQVNmLFFBQVEsR0FBRyx3QkFBd0I7a0VBRWxEZSxTQUFTZixRQUFRLGlCQUNoQiw4REFBQy9CLHVNQUFZQTs0REFBQzhHLFdBQVU7Ozs7O2tGQUV4Qiw4REFBQy9HLHVNQUFPQTs0REFBQytHLFdBQVU7Ozs7Ozs7Ozs7O2tFQUl2Qiw4REFBQ0k7d0RBQ0NELFNBQVMsQ0FBQ0U7NERBQ1JBLEVBQUVDLGVBQWU7NERBQ2pCOUIsYUFBYXhDO3dEQUNmO3dEQUNBZ0UsV0FBVTt3REFDVlcsT0FBTTtrRUFFTiw0RUFBQy9ILHdNQUFTQTs0REFBQ29ILFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0JBUWhDSixlQUFlRCw0QkFDZCw4REFBQ0k7b0JBQUlDLFdBQVU7K0JBQ1poRSxxQkFBQUEsU0FBU21CLFFBQVEsY0FBakJuQix5Q0FBQUEsbUJBQW1CNEUsR0FBRyxDQUFDLENBQUNDLFFBQVVwQixlQUFlb0IsT0FBT25CLFFBQVE7Ozs7Ozs7V0EzRzdEMUQsU0FBU1csRUFBRTs7Ozs7SUFnSHpCO0lBRUEsTUFBTW1FLG9CQUFvQjtRQUN4QmhILGNBQWM7UUFDZEUsbUJBQW1CO1FBQ25CYSxZQUFZO1lBQ1ZDLE1BQU07WUFDTkMsYUFBYTtZQUNiQyxVQUFVO1lBQ1ZDLFVBQVU7WUFDVkMsY0FBYztRQUNoQjtJQUNGO0lBRUEsTUFBTTZGLHNCQUFzQixDQUFDQztRQUMzQnpHLGtCQUFrQnlHO0lBQ3BCO0lBRUEsTUFBTUMscUJBQXFCLFNBQUNqRjtZQUFvQmtGLCtFQUF1QjtZQTZDbERsRjtRQTVDbkIsTUFBTThELGFBQWF6RyxDQUFBQSw2QkFBQUEsdUNBQUFBLGlCQUFrQnNELEVBQUUsTUFBS1gsU0FBU1csRUFBRTtRQUN2RCxNQUFNaUQsY0FBYzVELFNBQVNtQixRQUFRLElBQUluQixTQUFTbUIsUUFBUSxDQUFDMEMsTUFBTSxHQUFHO1FBRXBFLHFCQUNFLDhEQUFDRTtZQUVDQyxXQUFXLDZFQUlQa0IsT0FIRnBCLGFBQ0kseUNBQ0EsMkVBQ0wsS0FBK0IsT0FBNUJvQixjQUFjLFFBQVE7WUFDMUJmLFNBQVMsSUFBTTdHLGlCQUFpQjBDO3NCQUVoQyw0RUFBQytEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTs7b0NBQ1pKLDRCQUNDLDhEQUFDL0csdU1BQVVBO3dDQUFDbUgsV0FBVTs7Ozs7OERBRXRCLDhEQUFDbkgsdU1BQVVBO3dDQUFDbUgsV0FBVTs7Ozs7O2tEQUV4Qiw4REFBQ087d0NBQUdQLFdBQVcsaUJBQWdFLE9BQS9DRixhQUFhLGtCQUFrQjtrREFDNUQ5RCxTQUFTbEIsSUFBSTs7Ozs7O2tEQUVoQiw4REFBQzRGO3dDQUFLVixXQUFXLDhDQUloQixPQUhDaEUsU0FBU2YsUUFBUSxHQUNiLGdDQUNBO2tEQUVIZSxTQUFTZixRQUFRLEdBQUcsV0FBVzs7Ozs7Ozs7Ozs7OzRCQUluQ2UsU0FBU2pCLFdBQVcsa0JBQ25CLDhEQUFDeUY7Z0NBQUVSLFdBQVcsZ0JBQStELE9BQS9DRixhQUFhLGtCQUFrQjswQ0FDMUQ5RCxTQUFTakIsV0FBVzs7Ozs7OzBDQUl6Qiw4REFBQ2dGO2dDQUFJQyxXQUFVOztvQ0FDWmhFLFNBQVNvQixNQUFNLElBQUksT0FBT3BCLFNBQVNvQixNQUFNLENBQUNxRCxRQUFRLEtBQUssMEJBQ3RELDhEQUFDQzs7NENBQU0xRSxTQUFTb0IsTUFBTSxDQUFDcUQsUUFBUTs0Q0FBQzs7Ozs7OztvQ0FFakNiLDZCQUNDLDhEQUFDYzs7NkNBQU0xRSxxQkFBQUEsU0FBU21CLFFBQVEsY0FBakJuQix5Q0FBQUEsbUJBQW1CNkQsTUFBTTs0Q0FBQzs7Ozs7OztrREFFbkMsOERBQUNhOzs0Q0FBSzs0Q0FBUTFFLFNBQVNkLFlBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBSXZDLDhEQUFDNkU7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDSTtnQ0FDQ0QsU0FBUyxDQUFDRTtvQ0FDUkEsRUFBRUMsZUFBZTtvQ0FDakIvQixXQUFXdkM7Z0NBQ2I7Z0NBQ0FnRSxXQUFVO2dDQUNWVyxPQUFNOzBDQUVOLDRFQUFDaEksdU1BQVVBO29DQUFDcUgsV0FBVTs7Ozs7Ozs7Ozs7MENBR3hCLDhEQUFDSTtnQ0FDQ0QsU0FBUyxDQUFDRTtvQ0FDUkEsRUFBRUMsZUFBZTtvQ0FDakJ4QixtQkFBbUI5QztnQ0FDckI7Z0NBQ0FnRSxXQUFXLGlDQUlWLE9BSENoRSxTQUFTZixRQUFRLEdBQ2IsMERBQ0E7Z0NBRU4wRixPQUFPM0UsU0FBU2YsUUFBUSxHQUFHLHdCQUF3QjswQ0FFbERlLFNBQVNmLFFBQVEsaUJBQ2hCLDhEQUFDaEMsdU1BQU9BO29DQUFDK0csV0FBVTs7Ozs7MERBRW5CLDhEQUFDOUcsdU1BQVlBO29DQUFDOEcsV0FBVTs7Ozs7Ozs7Ozs7MENBSTVCLDhEQUFDSTtnQ0FDQ0QsU0FBUyxDQUFDRTtvQ0FDUkEsRUFBRUMsZUFBZTtvQ0FDakI5QixhQUFheEM7Z0NBQ2Y7Z0NBQ0FnRSxXQUFVO2dDQUNWVyxPQUFNOzBDQUVOLDRFQUFDL0gsd01BQVNBO29DQUFDb0gsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7V0FwRnRCaEUsU0FBU1csRUFBRTs7Ozs7SUEwRnRCO0lBRUEsTUFBTXdFLG1CQUFtQixDQUFDekQ7UUFDeEIsSUFBSTBELE1BQWtCLEVBQUU7UUFDeEIxRCxLQUFLakIsT0FBTyxDQUFDQyxDQUFBQTtZQUNYMEUsSUFBSTVELElBQUksQ0FBQ2Q7WUFDVCxJQUFJQSxJQUFJUyxRQUFRLElBQUlULElBQUlTLFFBQVEsQ0FBQzBDLE1BQU0sR0FBRyxHQUFHO2dCQUMzQ3VCLE1BQU1BLElBQUlDLE1BQU0sQ0FBQ0YsaUJBQWlCekUsSUFBSVMsUUFBUTtZQUNoRDtRQUNGO1FBQ0EsT0FBT2lFO0lBQ1Q7SUFFQSxxQkFDRSw4REFBQ3JCO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDN0csNERBQWNBO2dCQUNid0gsT0FBTTtnQkFDTjVGLGFBQVk7Z0JBQ1p1RyxtQkFBa0I7Z0JBQ2xCbEgsYUFBYUE7Z0JBQ2JtSCxnQkFBZ0JsSDtnQkFDaEJtSCxjQUFjO2dCQUNkQyxlQUFlO2dCQUNmQyxvQkFBb0I7Z0JBQ3BCQyxjQUFjO2dCQUNkQyxlQUFlZDtnQkFDZmUsa0JBQWlCO2dCQUNqQnJILFVBQVVBO2dCQUNWc0gsa0JBQWtCckg7Z0JBQ2xCVSxTQUFTQTtnQkFDVDRHLGlCQUFpQmhCO2dCQUNqQnpHLGdCQUFnQkE7Z0JBQ2hCMEgsV0FBV3ZJLG1CQUFtQm9HLE1BQU07Z0JBQ3BDb0MsWUFBWTFJLFdBQVdzRyxNQUFNOzs7Ozs7MEJBSS9CLDhEQUFDRTtnQkFBSUMsV0FBVTswQkFDWnJHLHdCQUNDLDhEQUFDb0c7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7Ozs7O3NDQUNmLDhEQUFDUTs0QkFBRVIsV0FBVTtzQ0FBcUI7Ozs7Ozs7Ozs7OzJCQUVsQ3ZHLG1CQUFtQm9HLE1BQU0sS0FBSyxrQkFDaEMsOERBQUNFO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ25ILHVNQUFVQTs0QkFBQ21ILFdBQVU7Ozs7OztzQ0FDdEIsOERBQUNPOzRCQUFHUCxXQUFVO3NDQUF5Qzs7Ozs7O3NDQUN2RCw4REFBQ1E7NEJBQUVSLFdBQVU7c0NBQ1Y1RixlQUFlOEgsT0FBT0MsSUFBSSxDQUFDN0gsZ0JBQWdCOEgsSUFBSSxDQUFDaEgsQ0FBQUEsTUFBT2QsY0FBYyxDQUFDYyxJQUFJLElBQ3ZFLGdEQUNBOzs7Ozs7c0NBRU4sOERBQUNnRjs0QkFDQ0QsU0FBU1c7NEJBQ1RkLFdBQVU7OzhDQUVWLDhEQUFDdEgsd01BQVFBO29DQUFDc0gsV0FBVTs7Ozs7O2dDQUFpQjs7Ozs7Ozs7Ozs7O3lDQUt6Qyw4REFBQ0Q7b0JBQUlDLFdBQVU7O3dCQUNaeEYsYUFBYSx5QkFDWiw4REFBQ3VGOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDcUM7Z0NBQU1yQyxXQUFVOztrREFDZiw4REFBQ3NDO3dDQUFNdEMsV0FBVTtrREFDZiw0RUFBQ3VDOzs4REFDQyw4REFBQ0M7b0RBQUd4QyxXQUFVOzhEQUErRTs7Ozs7OzhEQUc3Riw4REFBQ3dDO29EQUFHeEMsV0FBVTs4REFBK0U7Ozs7Ozs4REFHN0YsOERBQUN3QztvREFBR3hDLFdBQVU7OERBQStFOzs7Ozs7OERBRzdGLDhEQUFDd0M7b0RBQUd4QyxXQUFVOzhEQUErRTs7Ozs7OzhEQUc3Riw4REFBQ3dDO29EQUFHeEMsV0FBVTs4REFBZ0Y7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUtsRyw4REFBQ3lDO3dDQUFNekMsV0FBVTtrREFDZG1CLGlCQUFpQjFILG9CQUFvQm1ILEdBQUcsQ0FBQyxDQUFDNUU7Z0RBMEJoQ3pDLGtCQVFGeUM7aUVBakNQLDhEQUFDeEQsa0RBQU1BLENBQUMrSixFQUFFO2dEQUVSRyxTQUFTO29EQUFFQyxTQUFTO29EQUFHQyxHQUFHO2dEQUFHO2dEQUM3QkMsU0FBUztvREFBRUYsU0FBUztvREFBR0MsR0FBRztnREFBRTtnREFDNUI1QyxXQUFXLCtEQUVWLE9BREMzRyxDQUFBQSw2QkFBQUEsdUNBQUFBLGlCQUFrQnNELEVBQUUsTUFBS1gsU0FBU1csRUFBRSxHQUFHLDRDQUE0QztnREFFckZ3RCxTQUFTLElBQU03RyxpQkFBaUIwQzs7a0VBRWhDLDhEQUFDOEc7d0RBQUc5QyxXQUFVO2tFQUNaLDRFQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNEO29FQUFJQyxXQUFVOzhFQUNiLDRFQUFDbkgsdU1BQVVBO3dFQUFDbUgsV0FBVTs7Ozs7Ozs7Ozs7OEVBRXhCLDhEQUFDRDtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUNROzRFQUFFUixXQUFVO3NGQUE0Q2hFLFNBQVNsQixJQUFJOzs7Ozs7d0VBQ3JFa0IsU0FBU2pCLFdBQVcsa0JBQ25CLDhEQUFDeUY7NEVBQUVSLFdBQVU7c0ZBQWtDaEUsU0FBU2pCLFdBQVc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tFQUszRSw4REFBQytIO3dEQUFHOUMsV0FBVTtrRUFDWGhFLFNBQVNoQixRQUFRLGlCQUNoQiw4REFBQzBGOzREQUFLVixXQUFVO3NFQUNiekcsRUFBQUEsbUJBQUFBLFdBQVd3SixJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUVyRyxFQUFFLEtBQUtYLFNBQVNoQixRQUFRLGVBQS9DekIsdUNBQUFBLGlCQUFrRHVCLElBQUksS0FBSTs7Ozs7aUZBRzdELDhEQUFDNEY7NERBQUtWLFdBQVU7c0VBQStCOzs7Ozs7Ozs7OztrRUFHbkQsOERBQUM4Qzt3REFBRzlDLFdBQVU7a0VBQ1osNEVBQUNVOzREQUFLVixXQUFVOztnRUFDYmhFLEVBQUFBLG1CQUFBQSxTQUFTb0IsTUFBTSxjQUFmcEIsdUNBQUFBLGlCQUFpQnlFLFFBQVEsS0FBSTtnRUFBRTs7Ozs7Ozs7Ozs7O2tFQUdwQyw4REFBQ3FDO3dEQUFHOUMsV0FBVTtrRUFDWiw0RUFBQ1U7NERBQUtWLFdBQVcsdUVBSWhCLE9BSENoRSxTQUFTZixRQUFRLEdBQ2IsZ0NBQ0E7c0VBRUhlLFNBQVNmLFFBQVEsR0FBRyxXQUFXOzs7Ozs7Ozs7OztrRUFHcEMsOERBQUM2SDt3REFBRzlDLFdBQVU7a0VBQ1osNEVBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ0k7b0VBQ0NELFNBQVMsQ0FBQ0U7d0VBQ1JBLEVBQUVDLGVBQWU7d0VBQ2pCL0IsV0FBV3ZDO29FQUNiO29FQUNBZ0UsV0FBVTtvRUFDVlcsT0FBTTs4RUFFTiw0RUFBQ2hJLHVNQUFVQTt3RUFBQ3FILFdBQVU7Ozs7Ozs7Ozs7OzhFQUV4Qiw4REFBQ0k7b0VBQ0NELFNBQVMsQ0FBQ0U7d0VBQ1JBLEVBQUVDLGVBQWU7d0VBQ2pCeEIsbUJBQW1COUM7b0VBQ3JCO29FQUNBZ0UsV0FBVyw0R0FJVixPQUhDaEUsU0FBU2YsUUFBUSxHQUNiLDREQUNBLHdEQUNMO29FQUNEMEYsT0FBTzNFLFNBQVNmLFFBQVEsR0FBRyx3QkFBd0I7OEVBRWxEZSxTQUFTZixRQUFRLGlCQUNoQiw4REFBQy9CLHVNQUFZQTt3RUFBQzhHLFdBQVU7Ozs7OzZGQUV4Qiw4REFBQy9HLHVNQUFPQTt3RUFBQytHLFdBQVU7Ozs7Ozs7Ozs7OzhFQUd2Qiw4REFBQ0k7b0VBQ0NELFNBQVMsQ0FBQ0U7d0VBQ1JBLEVBQUVDLGVBQWU7d0VBQ2pCOUIsYUFBYXhDO29FQUNmO29FQUNBZ0UsV0FBVTtvRUFDVlcsT0FBTTs4RUFFTiw0RUFBQy9ILHdNQUFTQTt3RUFBQ29ILFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OytDQWxGdEJoRSxTQUFTVyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3dCQTZGM0JuQyxhQUFhLHdCQUNaLDhEQUFDdUY7NEJBQUlDLFdBQVU7c0NBQ1p2RyxtQkFBbUJtSCxHQUFHLENBQUMsQ0FBQzVFLFdBQWF5RCxlQUFlekQ7Ozs7Ozt3QkFJeER4QixhQUFhLHdCQUNaLDhEQUFDdUY7NEJBQUlDLFdBQVU7c0NBQ1ptQixpQkFBaUIxSCxvQkFBb0JtSCxHQUFHLENBQUMsQ0FBQzVFLFdBQWFpRixtQkFBbUJqRixVQUFVOzs7Ozs7d0JBSXhGeEIsYUFBYSx3QkFDWiw4REFBQ3VGOzRCQUFJQyxXQUFVO3NDQUNabUIsaUJBQWlCMUgsb0JBQW9CbUgsR0FBRyxDQUFDLENBQUM1RSxXQUFhaUYsbUJBQW1CakYsVUFBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUS9GLDhEQUFDdkQsMkRBQWVBOzBCQUNib0IsNEJBQ0MsOERBQUNyQixrREFBTUEsQ0FBQ3VILEdBQUc7b0JBQ1QyQyxTQUFTO3dCQUFFQyxTQUFTO29CQUFFO29CQUN0QkUsU0FBUzt3QkFBRUYsU0FBUztvQkFBRTtvQkFDdEJNLE1BQU07d0JBQUVOLFNBQVM7b0JBQUU7b0JBQ25CM0MsV0FBVTtvQkFDVkcsU0FBUyxJQUFNckcsY0FBYzs4QkFFN0IsNEVBQUN0QixrREFBTUEsQ0FBQ3VILEdBQUc7d0JBQ1QyQyxTQUFTOzRCQUFFUSxPQUFPOzRCQUFNUCxTQUFTO3dCQUFFO3dCQUNuQ0UsU0FBUzs0QkFBRUssT0FBTzs0QkFBR1AsU0FBUzt3QkFBRTt3QkFDaENNLE1BQU07NEJBQUVDLE9BQU87NEJBQU1QLFNBQVM7d0JBQUU7d0JBQ2hDM0MsV0FBVTt3QkFDVkcsU0FBUyxDQUFDRSxJQUFNQSxFQUFFQyxlQUFlOzswQ0FFakMsOERBQUNDO2dDQUFHUCxXQUFVOzBDQUNYakcsa0JBQWtCLGtCQUFrQjs7Ozs7OzBDQUd2Qyw4REFBQ29KO2dDQUFLQyxVQUFVLE9BQU8vQztvQ0FDckJBLEVBQUVnRCxjQUFjO29DQUVoQixJQUFJO3dDQUNGLE1BQU1DLE1BQU12SixrQkFDUix5QkFBNEMsT0FBbkJBLGdCQUFnQjRDLEVBQUUsSUFDM0M7d0NBRUosTUFBTStCLFNBQVMzRSxrQkFBa0IsUUFBUTt3Q0FFekMsTUFBTStELFdBQVcsTUFBTUMsTUFBTXVGLEtBQUs7NENBQ2hDNUU7NENBQ0FLLFNBQVM7Z0RBQUUsZ0JBQWdCOzRDQUFtQjs0Q0FDOUNDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztnREFDbkJyQyxXQUFXakMsU0FBU0UsSUFBSTtnREFDeEJnQyxXQUFXbEMsU0FBU0csV0FBVztnREFDL0JnQyxVQUFVbkMsU0FBU0ksUUFBUSxHQUFHbUUsT0FBT3ZFLFNBQVNJLFFBQVEsSUFBSTtnREFDMURpQyxVQUFVckMsU0FBU0ssUUFBUTtnREFDM0JpQyxjQUFjdEMsU0FBU00sWUFBWTs0Q0FDckM7d0NBQ0Y7d0NBRUEsSUFBSTRDLFNBQVNFLEVBQUUsRUFBRTs0Q0FDZmxFLGNBQWM7NENBQ2QyQjt3Q0FDRixPQUFPOzRDQUNMLE1BQU1rRCxZQUFZLE1BQU1iLFNBQVNJLElBQUk7NENBQ3JDVSxNQUFNRCxVQUFVRSxPQUFPLElBQUk7d0NBQzdCO29DQUNGLEVBQUUsT0FBT1IsT0FBTzt3Q0FDZEQsUUFBUUMsS0FBSyxDQUFDLDBCQUEwQkE7d0NBQ3hDTyxNQUFNO29DQUNSO2dDQUNGOztrREFDRSw4REFBQ21CO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7O2tFQUNDLDhEQUFDMUU7d0RBQU0yRSxXQUFVO2tFQUErQzs7Ozs7O2tFQUdoRSw4REFBQ3VEO3dEQUNDakksTUFBSzt3REFDTGtJLFFBQVE7d0RBQ1JoSSxPQUFPWixTQUFTRSxJQUFJO3dEQUNwQjJJLFVBQVUsQ0FBQ3BELElBQU14RixZQUFZO2dFQUFFLEdBQUdELFFBQVE7Z0VBQUVFLE1BQU11RixFQUFFcUQsTUFBTSxDQUFDbEksS0FBSzs0REFBQzt3REFDakV3RSxXQUFVOzs7Ozs7Ozs7Ozs7MERBSWQsOERBQUNEOztrRUFDQyw4REFBQzFFO3dEQUFNMkUsV0FBVTtrRUFBK0M7Ozs7OztrRUFHaEUsOERBQUMyRDt3REFDQ25JLE9BQU9aLFNBQVNHLFdBQVc7d0RBQzNCMEksVUFBVSxDQUFDcEQsSUFBTXhGLFlBQVk7Z0VBQUUsR0FBR0QsUUFBUTtnRUFBRUcsYUFBYXNGLEVBQUVxRCxNQUFNLENBQUNsSSxLQUFLOzREQUFDO3dEQUN4RW9JLE1BQU07d0RBQ041RCxXQUFVOzs7Ozs7Ozs7Ozs7MERBSWQsOERBQUNEOztrRUFDQyw4REFBQzFFO3dEQUFNMkUsV0FBVTtrRUFBK0M7Ozs7OztrRUFHaEUsOERBQUM2RDt3REFDQ3JJLE9BQU9aLFNBQVNJLFFBQVE7d0RBQ3hCeUksVUFBVSxDQUFDcEQsSUFBTXhGLFlBQVk7Z0VBQUUsR0FBR0QsUUFBUTtnRUFBRUksVUFBVXFGLEVBQUVxRCxNQUFNLENBQUNsSSxLQUFLOzREQUFDO3dEQUNyRXdFLFdBQVU7OzBFQUVWLDhEQUFDOEQ7Z0VBQU90SSxPQUFNOzBFQUFHOzs7Ozs7NERBQ2hCakMsV0FBV3FILEdBQUcsQ0FBQyxDQUFDbEUsb0JBQ2YsOERBQUNvSDtvRUFBb0J0SSxPQUFPa0IsSUFBSUMsRUFBRTs4RUFDL0JELElBQUk1QixJQUFJO21FQURFNEIsSUFBSUMsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBT3pCLDhEQUFDb0Q7Z0RBQUlDLFdBQVU7MERBQ2IsNEVBQUMzRTtvREFBTTJFLFdBQVU7O3NFQUNmLDhEQUFDdUQ7NERBQ0NqSSxNQUFLOzREQUNMeUksU0FBU25KLFNBQVNLLFFBQVE7NERBQzFCd0ksVUFBVSxDQUFDcEQsSUFBTXhGLFlBQVk7b0VBQUUsR0FBR0QsUUFBUTtvRUFBRUssVUFBVW9GLEVBQUVxRCxNQUFNLENBQUNLLE9BQU87Z0VBQUM7NERBQ3ZFL0QsV0FBVTs7Ozs7O3NFQUVaLDhEQUFDVTs0REFBS1YsV0FBVTtzRUFBNkI7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQUlqRCw4REFBQ0Q7O2tFQUNDLDhEQUFDMUU7d0RBQU0yRSxXQUFVO2tFQUErQzs7Ozs7O2tFQUdoRSw4REFBQ3VEO3dEQUNDakksTUFBSzt3REFDTEUsT0FBT1osU0FBU00sWUFBWTt3REFDNUJ1SSxVQUFVLENBQUNwRCxJQUFNeEYsWUFBWTtnRUFBRSxHQUFHRCxRQUFRO2dFQUFFTSxjQUFjaUUsT0FBT2tCLEVBQUVxRCxNQUFNLENBQUNsSSxLQUFLOzREQUFFO3dEQUNqRndFLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFLaEIsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0k7Z0RBQ0M5RSxNQUFLO2dEQUNMNkUsU0FBUyxJQUFNckcsY0FBYztnREFDN0JrRyxXQUFVOzBEQUNYOzs7Ozs7MERBR0QsOERBQUNJO2dEQUNDOUUsTUFBSztnREFDTDBFLFdBQVU7MERBRVRqRyxrQkFBa0IsV0FBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVVsRDtHQXh4QmdCWDtLQUFBQSIsInNvdXJjZXMiOlsiL1ZvbHVtZXMvRmlsZXMvVGVjaG5vbG93YXktTmV3LVdlYnNpdGUvVGVjaG5vbG93YXkvc3JjL2NvbXBvbmVudHMvYWRtaW4vc2VydmljZXMvY2F0ZWdvcnktbWFuYWdlbWVudC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IG1vdGlvbiwgQW5pbWF0ZVByZXNlbmNlIH0gZnJvbSAnZnJhbWVyLW1vdGlvbidcbmltcG9ydCB7XG4gIFBsdXNJY29uLFxuICBQZW5jaWxJY29uLFxuICBUcmFzaEljb24sXG4gIEZvbGRlckljb24sXG4gIEZvbGRlck9wZW5JY29uLFxuICBDaGV2cm9uUmlnaHRJY29uLFxuICBDaGV2cm9uRG93bkljb24sXG4gIEV5ZUljb24sXG4gIEV5ZVNsYXNoSWNvblxufSBmcm9tICdAaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUnXG5pbXBvcnQgeyBDYXRlZ29yeUhlYWRlciB9IGZyb20gJy4vY2F0ZWdvcnktaGVhZGVyJ1xuXG5pbnRlcmZhY2UgQ2F0ZWdvcnkge1xuICBpZDogc3RyaW5nXG4gIG5hbWU6IHN0cmluZ1xuICBkZXNjcmlwdGlvbj86IHN0cmluZ1xuICBwYXJlbnRJZD86IHN0cmluZ1xuICBpc0FjdGl2ZTogYm9vbGVhblxuICBkaXNwbGF5T3JkZXI6IG51bWJlclxuICBjaGlsZHJlbj86IENhdGVnb3J5W11cbiAgX2NvdW50Pzoge1xuICAgIHNlcnZpY2VzOiBudW1iZXJcbiAgICBjaGlsZHJlbjogbnVtYmVyXG4gIH1cbn1cblxuaW50ZXJmYWNlIENhdGVnb3J5TWFuYWdlbWVudFByb3BzIHtcbiAgc2VsZWN0ZWRDYXRlZ29yeTogQ2F0ZWdvcnkgfCBudWxsXG4gIG9uQ2F0ZWdvcnlTZWxlY3Q6IChjYXRlZ29yeTogQ2F0ZWdvcnkgfCBudWxsKSA9PiB2b2lkXG59XG5cbmludGVyZmFjZSBDYXRlZ29yeUZvcm1EYXRhIHtcbiAgbmFtZTogc3RyaW5nXG4gIGRlc2NyaXB0aW9uOiBzdHJpbmdcbiAgcGFyZW50SWQ6IHN0cmluZ1xuICBpc0FjdGl2ZTogYm9vbGVhblxuICBkaXNwbGF5T3JkZXI6IG51bWJlclxufVxuXG5leHBvcnQgZnVuY3Rpb24gQ2F0ZWdvcnlNYW5hZ2VtZW50KHsgc2VsZWN0ZWRDYXRlZ29yeSwgb25DYXRlZ29yeVNlbGVjdCB9OiBDYXRlZ29yeU1hbmFnZW1lbnRQcm9wcykge1xuICBjb25zdCBbY2F0ZWdvcmllcywgc2V0Q2F0ZWdvcmllc10gPSB1c2VTdGF0ZTxDYXRlZ29yeVtdPihbXSlcbiAgY29uc3QgW2ZpbHRlcmVkQ2F0ZWdvcmllcywgc2V0RmlsdGVyZWRDYXRlZ29yaWVzXSA9IHVzZVN0YXRlPENhdGVnb3J5W10+KFtdKVxuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKVxuICBjb25zdCBbaXNGb3JtT3Blbiwgc2V0SXNGb3JtT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW2VkaXRpbmdDYXRlZ29yeSwgc2V0RWRpdGluZ0NhdGVnb3J5XSA9IHVzZVN0YXRlPENhdGVnb3J5IHwgbnVsbD4obnVsbClcbiAgY29uc3QgW2V4cGFuZGVkQ2F0ZWdvcmllcywgc2V0RXhwYW5kZWRDYXRlZ29yaWVzXSA9IHVzZVN0YXRlPFNldDxzdHJpbmc+PihuZXcgU2V0KCkpXG4gIGNvbnN0IFtzZWFyY2hRdWVyeSwgc2V0U2VhcmNoUXVlcnldID0gdXNlU3RhdGUoJycpXG4gIGNvbnN0IFtjdXJyZW50RmlsdGVycywgc2V0Q3VycmVudEZpbHRlcnNdID0gdXNlU3RhdGU8UmVjb3JkPHN0cmluZywgc3RyaW5nPj4oe30pXG4gIGNvbnN0IFt2aWV3TW9kZSwgc2V0Vmlld01vZGVdID0gdXNlU3RhdGU8J3RhYmxlJyB8ICdsaXN0JyB8ICdncmlkJyB8ICdjYXJkJz4oJ2xpc3QnKVxuICBjb25zdCBbZGVuc2l0eSwgc2V0RGVuc2l0eV0gPSB1c2VTdGF0ZTwnY29tcGFjdCcgfCAnY29tZm9ydGFibGUnIHwgJ3NwYWNpb3VzJz4oJ2NvbWZvcnRhYmxlJylcblxuICBjb25zdCBbZm9ybURhdGEsIHNldEZvcm1EYXRhXSA9IHVzZVN0YXRlPENhdGVnb3J5Rm9ybURhdGE+KHtcbiAgICBuYW1lOiAnJyxcbiAgICBkZXNjcmlwdGlvbjogJycsXG4gICAgcGFyZW50SWQ6ICcnLFxuICAgIGlzQWN0aXZlOiB0cnVlLFxuICAgIGRpc3BsYXlPcmRlcjogMFxuICB9KVxuXG4gIGNvbnN0IGZpbHRlcnMgPSBbXG4gICAge1xuICAgICAga2V5OiAnc3RhdHVzJyxcbiAgICAgIGxhYmVsOiAnU3RhdHVzJyxcbiAgICAgIHR5cGU6ICdzZWxlY3QnIGFzIGNvbnN0LFxuICAgICAgb3B0aW9uczogW1xuICAgICAgICB7IHZhbHVlOiAnJywgbGFiZWw6ICdBbGwgU3RhdHVzJyB9LFxuICAgICAgICB7IHZhbHVlOiAnYWN0aXZlJywgbGFiZWw6ICdBY3RpdmUnIH0sXG4gICAgICAgIHsgdmFsdWU6ICdpbmFjdGl2ZScsIGxhYmVsOiAnSW5hY3RpdmUnIH1cbiAgICAgIF1cbiAgICB9LFxuICAgIHtcbiAgICAgIGtleTogJ3BhcmVudCcsXG4gICAgICBsYWJlbDogJ1BhcmVudCBDYXRlZ29yeScsXG4gICAgICB0eXBlOiAnc2VsZWN0JyBhcyBjb25zdCxcbiAgICAgIG9wdGlvbnM6IFtcbiAgICAgICAgeyB2YWx1ZTogJycsIGxhYmVsOiAnQWxsIENhdGVnb3JpZXMnIH0sXG4gICAgICAgIHsgdmFsdWU6ICdyb290JywgbGFiZWw6ICdSb290IENhdGVnb3JpZXMnIH0sXG4gICAgICAgIHsgdmFsdWU6ICdzdWInLCBsYWJlbDogJ1N1YiBDYXRlZ29yaWVzJyB9XG4gICAgICBdXG4gICAgfVxuICBdXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBmZXRjaENhdGVnb3JpZXMoKVxuICB9LCBbXSlcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGZpbHRlckFuZFNvcnRDYXRlZ29yaWVzKClcbiAgfSwgW2NhdGVnb3JpZXMsIHNlYXJjaFF1ZXJ5LCBjdXJyZW50RmlsdGVyc10pXG5cbiAgY29uc3QgZmlsdGVyQW5kU29ydENhdGVnb3JpZXMgPSAoKSA9PiB7XG4gICAgbGV0IGZpbHRlcmVkID0gWy4uLmNhdGVnb3JpZXNdXG5cbiAgICAvLyBBcHBseSBzZWFyY2ggZmlsdGVyXG4gICAgaWYgKHNlYXJjaFF1ZXJ5LnRyaW0oKSkge1xuICAgICAgY29uc3Qgc2VhcmNoTG93ZXIgPSBzZWFyY2hRdWVyeS50b0xvd2VyQ2FzZSgpXG4gICAgICBmaWx0ZXJlZCA9IGZpbHRlcmVkLmZpbHRlcihjYXRlZ29yeSA9PlxuICAgICAgICBjYXRlZ29yeS5uYW1lLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoTG93ZXIpIHx8XG4gICAgICAgIChjYXRlZ29yeS5kZXNjcmlwdGlvbiAmJiBjYXRlZ29yeS5kZXNjcmlwdGlvbi50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaExvd2VyKSlcbiAgICAgIClcbiAgICB9XG5cbiAgICAvLyBBcHBseSBzdGF0dXMgZmlsdGVyXG4gICAgaWYgKGN1cnJlbnRGaWx0ZXJzLnN0YXR1cykge1xuICAgICAgaWYgKGN1cnJlbnRGaWx0ZXJzLnN0YXR1cyA9PT0gJ2FjdGl2ZScpIHtcbiAgICAgICAgZmlsdGVyZWQgPSBmaWx0ZXJlZC5maWx0ZXIoY2F0ZWdvcnkgPT4gY2F0ZWdvcnkuaXNBY3RpdmUpXG4gICAgICB9IGVsc2UgaWYgKGN1cnJlbnRGaWx0ZXJzLnN0YXR1cyA9PT0gJ2luYWN0aXZlJykge1xuICAgICAgICBmaWx0ZXJlZCA9IGZpbHRlcmVkLmZpbHRlcihjYXRlZ29yeSA9PiAhY2F0ZWdvcnkuaXNBY3RpdmUpXG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gQXBwbHkgcGFyZW50IGZpbHRlclxuICAgIGlmIChjdXJyZW50RmlsdGVycy5wYXJlbnQpIHtcbiAgICAgIGlmIChjdXJyZW50RmlsdGVycy5wYXJlbnQgPT09ICdyb290Jykge1xuICAgICAgICBmaWx0ZXJlZCA9IGZpbHRlcmVkLmZpbHRlcihjYXRlZ29yeSA9PiAhY2F0ZWdvcnkucGFyZW50SWQpXG4gICAgICB9IGVsc2UgaWYgKGN1cnJlbnRGaWx0ZXJzLnBhcmVudCA9PT0gJ3N1YicpIHtcbiAgICAgICAgZmlsdGVyZWQgPSBmaWx0ZXJlZC5maWx0ZXIoY2F0ZWdvcnkgPT4gY2F0ZWdvcnkucGFyZW50SWQpXG4gICAgICB9XG4gICAgfVxuXG4gICAgc2V0RmlsdGVyZWRDYXRlZ29yaWVzKGZpbHRlcmVkKVxuICB9XG5cbiAgY29uc3QgYnVpbGRDYXRlZ29yeVRyZWUgPSAoZmxhdENhdGVnb3JpZXM6IGFueVtdKTogQ2F0ZWdvcnlbXSA9PiB7XG4gICAgY29uc3QgY2F0ZWdvcnlNYXAgPSBuZXcgTWFwKClcbiAgICBjb25zdCByb290Q2F0ZWdvcmllczogQ2F0ZWdvcnlbXSA9IFtdXG5cbiAgICAvLyBUcmFuc2Zvcm0gYW5kIGNyZWF0ZSBtYXBcbiAgICBmbGF0Q2F0ZWdvcmllcy5mb3JFYWNoKGNhdCA9PiB7XG4gICAgICBjb25zdCBjYXRlZ29yeTogQ2F0ZWdvcnkgPSB7XG4gICAgICAgIGlkOiBTdHJpbmcoY2F0LmlkKSxcbiAgICAgICAgbmFtZTogY2F0LmNhdGVnbmFtZSB8fCBjYXQubmFtZSxcbiAgICAgICAgZGVzY3JpcHRpb246IGNhdC5jYXRlZ2Rlc2MgfHwgY2F0LmRlc2NyaXB0aW9uLFxuICAgICAgICBwYXJlbnRJZDogY2F0LnBhcmVudGlkID8gU3RyaW5nKGNhdC5wYXJlbnRpZCkgOiB1bmRlZmluZWQsXG4gICAgICAgIGlzQWN0aXZlOiBjYXQuaXNhY3RpdmUsXG4gICAgICAgIGRpc3BsYXlPcmRlcjogY2F0LmRpc3BsYXlvcmRlciB8fCAwLFxuICAgICAgICBjaGlsZHJlbjogW10sXG4gICAgICAgIF9jb3VudDogY2F0Ll9jb3VudFxuICAgICAgfVxuICAgICAgY2F0ZWdvcnlNYXAuc2V0KGNhdGVnb3J5LmlkLCBjYXRlZ29yeSlcbiAgICB9KVxuXG4gICAgLy8gQnVpbGQgdHJlZSBzdHJ1Y3R1cmVcbiAgICBjYXRlZ29yeU1hcC5mb3JFYWNoKGNhdGVnb3J5ID0+IHtcbiAgICAgIGlmIChjYXRlZ29yeS5wYXJlbnRJZCAmJiBjYXRlZ29yeU1hcC5oYXMoY2F0ZWdvcnkucGFyZW50SWQpKSB7XG4gICAgICAgIGNhdGVnb3J5TWFwLmdldChjYXRlZ29yeS5wYXJlbnRJZCkuY2hpbGRyZW4ucHVzaChjYXRlZ29yeSlcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHJvb3RDYXRlZ29yaWVzLnB1c2goY2F0ZWdvcnkpXG4gICAgICB9XG4gICAgfSlcblxuICAgIC8vIFNvcnQgYnkgZGlzcGxheSBvcmRlclxuICAgIGNvbnN0IHNvcnRDYXRlZ29yaWVzID0gKGNhdHM6IENhdGVnb3J5W10pID0+IHtcbiAgICAgIGNhdHMuc29ydCgoYSwgYikgPT4gYS5kaXNwbGF5T3JkZXIgLSBiLmRpc3BsYXlPcmRlcilcbiAgICAgIGNhdHMuZm9yRWFjaChjYXQgPT4ge1xuICAgICAgICBpZiAoY2F0LmNoaWxkcmVuKSB7XG4gICAgICAgICAgc29ydENhdGVnb3JpZXMoY2F0LmNoaWxkcmVuKVxuICAgICAgICB9XG4gICAgICB9KVxuICAgIH1cblxuICAgIHNvcnRDYXRlZ29yaWVzKHJvb3RDYXRlZ29yaWVzKVxuICAgIHJldHVybiByb290Q2F0ZWdvcmllc1xuICB9XG5cbiAgY29uc3QgZmV0Y2hDYXRlZ29yaWVzID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRMb2FkaW5nKHRydWUpXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2FkbWluL2NhdGVnb3JpZXM/bGltaXQ9MTAwJylcblxuICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKClcbiAgICAgICAgY29uc3QgY2F0ZWdvcmllc0RhdGEgPSBkYXRhLmRhdGEgfHwgZGF0YS5jYXRlZ29yaWVzIHx8IFtdXG4gICAgICAgIHNldENhdGVnb3JpZXMoYnVpbGRDYXRlZ29yeVRyZWUoY2F0ZWdvcmllc0RhdGEpKVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGZldGNoIGNhdGVnb3JpZXM6JywgcmVzcG9uc2Uuc3RhdHVzLCByZXNwb25zZS5zdGF0dXNUZXh0KVxuICAgICAgICBzZXRDYXRlZ29yaWVzKFtdKVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBjYXRlZ29yaWVzOicsIGVycm9yKVxuICAgICAgc2V0Q2F0ZWdvcmllcyhbXSlcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBoYW5kbGVFZGl0ID0gKGNhdGVnb3J5OiBDYXRlZ29yeSkgPT4ge1xuICAgIHNldEVkaXRpbmdDYXRlZ29yeShjYXRlZ29yeSlcbiAgICBzZXRGb3JtRGF0YSh7XG4gICAgICBuYW1lOiBjYXRlZ29yeS5uYW1lLFxuICAgICAgZGVzY3JpcHRpb246IGNhdGVnb3J5LmRlc2NyaXB0aW9uIHx8ICcnLFxuICAgICAgcGFyZW50SWQ6IGNhdGVnb3J5LnBhcmVudElkIHx8ICcnLFxuICAgICAgaXNBY3RpdmU6IGNhdGVnb3J5LmlzQWN0aXZlLFxuICAgICAgZGlzcGxheU9yZGVyOiBjYXRlZ29yeS5kaXNwbGF5T3JkZXJcbiAgICB9KVxuICAgIHNldElzRm9ybU9wZW4odHJ1ZSlcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZURlbGV0ZSA9IGFzeW5jIChjYXRlZ29yeTogQ2F0ZWdvcnkpID0+IHtcbiAgICBpZiAoIWNvbmZpcm0oYEFyZSB5b3Ugc3VyZSB5b3Ugd2FudCB0byBkZWxldGUgXCIke2NhdGVnb3J5Lm5hbWV9XCI/YCkpIHJldHVyblxuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvYWRtaW4vY2F0ZWdvcmllcy8ke2NhdGVnb3J5LmlkfWAsIHtcbiAgICAgICAgbWV0aG9kOiAnREVMRVRFJyxcbiAgICAgIH0pXG5cbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICBhd2FpdCBmZXRjaENhdGVnb3JpZXMoKVxuICAgICAgICBpZiAoc2VsZWN0ZWRDYXRlZ29yeT8uaWQgPT09IGNhdGVnb3J5LmlkKSB7XG4gICAgICAgICAgb25DYXRlZ29yeVNlbGVjdChudWxsKVxuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zdCBlcnJvckRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKClcbiAgICAgICAgYWxlcnQoZXJyb3JEYXRhLm1lc3NhZ2UgfHwgJ0ZhaWxlZCB0byBkZWxldGUgY2F0ZWdvcnknKVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBkZWxldGluZyBjYXRlZ29yeTonLCBlcnJvcilcbiAgICAgIGFsZXJ0KCdBbiBlcnJvciBvY2N1cnJlZCB3aGlsZSBkZWxldGluZyB0aGUgY2F0ZWdvcnknKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVRvZ2dsZUFjdGl2ZSA9IGFzeW5jIChjYXRlZ29yeTogQ2F0ZWdvcnkpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS9hZG1pbi9jYXRlZ29yaWVzLyR7Y2F0ZWdvcnkuaWR9YCwge1xuICAgICAgICBtZXRob2Q6ICdQVVQnLFxuICAgICAgICBoZWFkZXJzOiB7ICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICAgIGNhdGVnbmFtZTogY2F0ZWdvcnkubmFtZSxcbiAgICAgICAgICBjYXRlZ2Rlc2M6IGNhdGVnb3J5LmRlc2NyaXB0aW9uLFxuICAgICAgICAgIHBhcmVudGlkOiBjYXRlZ29yeS5wYXJlbnRJZCA/IE51bWJlcihjYXRlZ29yeS5wYXJlbnRJZCkgOiAwLFxuICAgICAgICAgIGlzYWN0aXZlOiAhY2F0ZWdvcnkuaXNBY3RpdmUsXG4gICAgICAgICAgZGlzcGxheW9yZGVyOiBjYXRlZ29yeS5kaXNwbGF5T3JkZXJcbiAgICAgICAgfSlcbiAgICAgIH0pXG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgZmV0Y2hDYXRlZ29yaWVzKClcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgdG9nZ2xpbmcgY2F0ZWdvcnkgc3RhdHVzOicsIGVycm9yKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IHRvZ2dsZUV4cGFuZGVkID0gKGNhdGVnb3J5SWQ6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IG5ld0V4cGFuZGVkID0gbmV3IFNldChleHBhbmRlZENhdGVnb3JpZXMpXG4gICAgaWYgKG5ld0V4cGFuZGVkLmhhcyhjYXRlZ29yeUlkKSkge1xuICAgICAgbmV3RXhwYW5kZWQuZGVsZXRlKGNhdGVnb3J5SWQpXG4gICAgfSBlbHNlIHtcbiAgICAgIG5ld0V4cGFuZGVkLmFkZChjYXRlZ29yeUlkKVxuICAgIH1cbiAgICBzZXRFeHBhbmRlZENhdGVnb3JpZXMobmV3RXhwYW5kZWQpXG4gIH1cblxuICBjb25zdCByZW5kZXJDYXRlZ29yeSA9IChjYXRlZ29yeTogQ2F0ZWdvcnksIGxldmVsOiBudW1iZXIgPSAwKSA9PiB7XG4gICAgY29uc3QgaXNFeHBhbmRlZCA9IGV4cGFuZGVkQ2F0ZWdvcmllcy5oYXMoY2F0ZWdvcnkuaWQpXG4gICAgY29uc3QgaGFzQ2hpbGRyZW4gPSBjYXRlZ29yeS5jaGlsZHJlbiAmJiBjYXRlZ29yeS5jaGlsZHJlbi5sZW5ndGggPiAwXG4gICAgY29uc3QgaXNTZWxlY3RlZCA9IHNlbGVjdGVkQ2F0ZWdvcnk/LmlkID09PSBjYXRlZ29yeS5pZFxuXG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYga2V5PXtjYXRlZ29yeS5pZH0gY2xhc3NOYW1lPVwic2VsZWN0LW5vbmVcIj5cbiAgICAgICAgPGRpdlxuICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBweS0yIHB4LTMgcm91bmRlZC1ub25lIGN1cnNvci1wb2ludGVyIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICBpc1NlbGVjdGVkXG4gICAgICAgICAgICAgID8gJ2JnLWJsdWUtNTAgYm9yZGVyIGJvcmRlci1ibHVlLTIwMCdcbiAgICAgICAgICAgICAgOiAnaG92ZXI6YmctZ3JheS0xMDAnXG4gICAgICAgICAgfWB9XG4gICAgICAgICAgc3R5bGU9e3sgbWFyZ2luTGVmdDogYCR7bGV2ZWwgKiAyMH1weGAgfX1cbiAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBvbkNhdGVnb3J5U2VsZWN0KGNhdGVnb3J5KX1cbiAgICAgICAgPlxuICAgICAgICAgIHtoYXNDaGlsZHJlbiA/IChcbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KGUpID0+IHtcbiAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpXG4gICAgICAgICAgICAgICAgdG9nZ2xlRXhwYW5kZWQoY2F0ZWdvcnkuaWQpXG4gICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMSBob3ZlcjpiZy1ncmF5LTIwMCByb3VuZGVkLW5vbmVcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7aXNFeHBhbmRlZCA/IChcbiAgICAgICAgICAgICAgICA8Q2hldnJvbkRvd25JY29uIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1ncmF5LTUwMFwiIC8+XG4gICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgPENoZXZyb25SaWdodEljb24gY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWdyYXktNTAwXCIgLz5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNlwiIC8+XG4gICAgICAgICAgKX1cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIGZsZXgtMVwiPlxuICAgICAgICAgICAge2hhc0NoaWxkcmVuID8gKFxuICAgICAgICAgICAgICBpc0V4cGFuZGVkID8gKFxuICAgICAgICAgICAgICAgIDxGb2xkZXJPcGVuSWNvbiBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtYmx1ZS01MDBcIiAvPlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIDxGb2xkZXJJY29uIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1ibHVlLTUwMFwiIC8+XG4gICAgICAgICAgICAgIClcbiAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgIDxGb2xkZXJJY29uIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1ncmF5LTQwMFwiIC8+XG4gICAgICAgICAgICApfVxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPXtgZm9udC1ib2xkIHRleHQtbGcgJHtpc1NlbGVjdGVkID8gJ3RleHQtYmx1ZS05MDAnIDogJ3RleHQtZ3JheS05MDAnfWB9PlxuICAgICAgICAgICAgICAgICAgICB7Y2F0ZWdvcnkubmFtZX1cbiAgICAgICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgICAgICB7Y2F0ZWdvcnkuZGVzY3JpcHRpb24gJiYgKFxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9e2B0ZXh0LWJhc2UgJHtpc1NlbGVjdGVkID8gJ3RleHQtYmx1ZS02MDAnIDogJ3RleHQtZ3JheS02MDAnfWB9PlxuICAgICAgICAgICAgICAgICAgICAgIHtjYXRlZ29yeS5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICAgICAge2NhdGVnb3J5Ll9jb3VudCAmJiB0eXBlb2YgY2F0ZWdvcnkuX2NvdW50LnNlcnZpY2VzID09PSAnbnVtYmVyJyAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImJnLWJsdWUtMTAwIHRleHQtYmx1ZS04MDAgcHgtMyBweS0xIHJvdW5kZWQtbm9uZSB0ZXh0LWJhc2UgZm9udC1ib2xkXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2NhdGVnb3J5Ll9jb3VudC5zZXJ2aWNlc30gc2VydmljZXNcbiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKClcbiAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVFZGl0KGNhdGVnb3J5KVxuICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtMiBweS0xIGJvcmRlciBib3JkZXItdHJhbnNwYXJlbnQgdGV4dC14cyBmb250LW1lZGl1bSByb3VuZGVkLW5vbmUgdGV4dC13aGl0ZSBiZy1ibHVlLTYwMCBob3ZlcjpiZy1ibHVlLTcwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctb2Zmc2V0LTIgZm9jdXM6cmluZy1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgICAgICAgIHRpdGxlPVwiRWRpdCBjYXRlZ29yeVwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxQZW5jaWxJY29uIGNsYXNzTmFtZT1cImgtMyB3LTNcIiAvPlxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpXG4gICAgICAgICAgICAgICAgICAgICAgaGFuZGxlVG9nZ2xlQWN0aXZlKGNhdGVnb3J5KVxuICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtMiBweS0xIGJvcmRlciBib3JkZXItdHJhbnNwYXJlbnQgdGV4dC14cyBmb250LW1lZGl1bSByb3VuZGVkLW5vbmUgdGV4dC13aGl0ZSAke1xuICAgICAgICAgICAgICAgICAgICAgIGNhdGVnb3J5LmlzQWN0aXZlXG4gICAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1vcmFuZ2UtNjAwIGhvdmVyOmJnLW9yYW5nZS03MDAgZm9jdXM6cmluZy1vcmFuZ2UtNTAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgOiAnYmctZ3JlZW4tNjAwIGhvdmVyOmJnLWdyZWVuLTcwMCBmb2N1czpyaW5nLWdyZWVuLTUwMCdcbiAgICAgICAgICAgICAgICAgICAgfSBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctb2Zmc2V0LTJgfVxuICAgICAgICAgICAgICAgICAgICB0aXRsZT17Y2F0ZWdvcnkuaXNBY3RpdmUgPyAnRGVhY3RpdmF0ZSBjYXRlZ29yeScgOiAnQWN0aXZhdGUgY2F0ZWdvcnknfVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICB7Y2F0ZWdvcnkuaXNBY3RpdmUgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgPEV5ZVNsYXNoSWNvbiBjbGFzc05hbWU9XCJoLTMgdy0zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICA8RXllSWNvbiBjbGFzc05hbWU9XCJoLTMgdy0zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKVxuICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZURlbGV0ZShjYXRlZ29yeSlcbiAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTIgcHktMSBib3JkZXIgYm9yZGVyLXRyYW5zcGFyZW50IHRleHQteHMgZm9udC1tZWRpdW0gcm91bmRlZC1ub25lIHRleHQtd2hpdGUgYmctcmVkLTYwMCBob3ZlcjpiZy1yZWQtNzAwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1vZmZzZXQtMiBmb2N1czpyaW5nLXJlZC01MDBcIlxuICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIkRlbGV0ZSBjYXRlZ29yeVwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxUcmFzaEljb24gY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAge2hhc0NoaWxkcmVuICYmIGlzRXhwYW5kZWQgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWwtNlwiPlxuICAgICAgICAgICAge2NhdGVnb3J5LmNoaWxkcmVuPy5tYXAoKGNoaWxkKSA9PiByZW5kZXJDYXRlZ29yeShjaGlsZCwgbGV2ZWwgKyAxKSl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cbiAgICApXG4gIH1cblxuICBjb25zdCBoYW5kbGVDcmVhdGVDbGljayA9ICgpID0+IHtcbiAgICBzZXRJc0Zvcm1PcGVuKHRydWUpXG4gICAgc2V0RWRpdGluZ0NhdGVnb3J5KG51bGwpXG4gICAgc2V0Rm9ybURhdGEoe1xuICAgICAgbmFtZTogJycsXG4gICAgICBkZXNjcmlwdGlvbjogJycsXG4gICAgICBwYXJlbnRJZDogJycsXG4gICAgICBpc0FjdGl2ZTogdHJ1ZSxcbiAgICAgIGRpc3BsYXlPcmRlcjogMFxuICAgIH0pXG4gIH1cblxuICBjb25zdCBoYW5kbGVGaWx0ZXJzQ2hhbmdlID0gKG5ld0ZpbHRlcnM6IFJlY29yZDxzdHJpbmcsIHN0cmluZz4pID0+IHtcbiAgICBzZXRDdXJyZW50RmlsdGVycyhuZXdGaWx0ZXJzKVxuICB9XG5cbiAgY29uc3QgcmVuZGVyQ2F0ZWdvcnlDYXJkID0gKGNhdGVnb3J5OiBDYXRlZ29yeSwgaXNMYXJnZUNhcmQ6IGJvb2xlYW4gPSBmYWxzZSkgPT4ge1xuICAgIGNvbnN0IGlzU2VsZWN0ZWQgPSBzZWxlY3RlZENhdGVnb3J5Py5pZCA9PT0gY2F0ZWdvcnkuaWRcbiAgICBjb25zdCBoYXNDaGlsZHJlbiA9IGNhdGVnb3J5LmNoaWxkcmVuICYmIGNhdGVnb3J5LmNoaWxkcmVuLmxlbmd0aCA+IDBcblxuICAgIHJldHVybiAoXG4gICAgICA8ZGl2XG4gICAgICAgIGtleT17Y2F0ZWdvcnkuaWR9XG4gICAgICAgIGNsYXNzTmFtZT17YGJnLWdyYXktNTAgYm9yZGVyIHJvdW5kZWQtbm9uZSBjdXJzb3ItcG9pbnRlciB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgJHtcbiAgICAgICAgICBpc1NlbGVjdGVkXG4gICAgICAgICAgICA/ICdib3JkZXItYmx1ZS01MDAgc2hhZG93LW1kIGJnLWJsdWUtNTAnXG4gICAgICAgICAgICA6ICdib3JkZXItZ3JheS0yMDAgaG92ZXI6Ym9yZGVyLWdyYXktMzAwIGhvdmVyOnNoYWRvdy1zbSBob3ZlcjpiZy1ncmF5LTEwMCdcbiAgICAgICAgfSAke2lzTGFyZ2VDYXJkID8gJ3AtNicgOiAncC00J31gfVxuICAgICAgICBvbkNsaWNrPXsoKSA9PiBvbkNhdGVnb3J5U2VsZWN0KGNhdGVnb3J5KX1cbiAgICAgID5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBtYi0yXCI+XG4gICAgICAgICAgICAgIHtoYXNDaGlsZHJlbiA/IChcbiAgICAgICAgICAgICAgICA8Rm9sZGVySWNvbiBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtYmx1ZS01MDBcIiAvPlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIDxGb2xkZXJJY29uIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1ncmF5LTQwMFwiIC8+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9e2Bmb250LXNlbWlib2xkICR7aXNTZWxlY3RlZCA/ICd0ZXh0LWJsdWUtOTAwJyA6ICd0ZXh0LWdyYXktOTAwJ31gfT5cbiAgICAgICAgICAgICAgICB7Y2F0ZWdvcnkubmFtZX1cbiAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgcHgtMiBweS0xIHJvdW5kZWQtZnVsbCB0ZXh0LXhzIGZvbnQtbWVkaXVtICR7XG4gICAgICAgICAgICAgICAgY2F0ZWdvcnkuaXNBY3RpdmUgXG4gICAgICAgICAgICAgICAgICA/ICdiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi04MDAnIFxuICAgICAgICAgICAgICAgICAgOiAnYmctZ3JheS0xMDAgdGV4dC1ncmF5LTYwMCdcbiAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgIHtjYXRlZ29yeS5pc0FjdGl2ZSA/ICdBY3RpdmUnIDogJ0luYWN0aXZlJ31cbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIHtjYXRlZ29yeS5kZXNjcmlwdGlvbiAmJiAoXG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT17YHRleHQtc20gbWItMyAke2lzU2VsZWN0ZWQgPyAndGV4dC1ibHVlLTYwMCcgOiAndGV4dC1ncmF5LTYwMCd9YH0+XG4gICAgICAgICAgICAgICAge2NhdGVnb3J5LmRlc2NyaXB0aW9ufVxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICApfVxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNCB0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAge2NhdGVnb3J5Ll9jb3VudCAmJiB0eXBlb2YgY2F0ZWdvcnkuX2NvdW50LnNlcnZpY2VzID09PSAnbnVtYmVyJyAmJiAoXG4gICAgICAgICAgICAgICAgPHNwYW4+e2NhdGVnb3J5Ll9jb3VudC5zZXJ2aWNlc30gc2VydmljZXM8L3NwYW4+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIHtoYXNDaGlsZHJlbiAmJiAoXG4gICAgICAgICAgICAgICAgPHNwYW4+e2NhdGVnb3J5LmNoaWxkcmVuPy5sZW5ndGh9IHN1YmNhdGVnb3JpZXM8L3NwYW4+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDxzcGFuPk9yZGVyOiB7Y2F0ZWdvcnkuZGlzcGxheU9yZGVyfTwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTEgbWwtNFwiPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKClcbiAgICAgICAgICAgICAgICBoYW5kbGVFZGl0KGNhdGVnb3J5KVxuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtYmx1ZS02MDAgaG92ZXI6YmctYmx1ZS01MCByb3VuZGVkIHAtMVwiXG4gICAgICAgICAgICAgIHRpdGxlPVwiRWRpdCBjYXRlZ29yeVwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxQZW5jaWxJY29uIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KGUpID0+IHtcbiAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpXG4gICAgICAgICAgICAgICAgaGFuZGxlVG9nZ2xlQWN0aXZlKGNhdGVnb3J5KVxuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2Byb3VuZGVkIHRyYW5zaXRpb24tY29sb3JzIHAtMSAke1xuICAgICAgICAgICAgICAgIGNhdGVnb3J5LmlzQWN0aXZlXG4gICAgICAgICAgICAgICAgICA/ICd0ZXh0LWdyZWVuLTYwMCBob3Zlcjp0ZXh0LWdyZWVuLTcwMCBob3ZlcjpiZy1ncmVlbi01MCdcbiAgICAgICAgICAgICAgICAgIDogJ3RleHQtZ3JheS00MDAgaG92ZXI6dGV4dC1ncmF5LTYwMCBob3ZlcjpiZy1ncmF5LTUwJ1xuICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgdGl0bGU9e2NhdGVnb3J5LmlzQWN0aXZlID8gJ0RlYWN0aXZhdGUgY2F0ZWdvcnknIDogJ0FjdGl2YXRlIGNhdGVnb3J5J31cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge2NhdGVnb3J5LmlzQWN0aXZlID8gKFxuICAgICAgICAgICAgICAgIDxFeWVJY29uIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIDxFeWVTbGFzaEljb24gY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKClcbiAgICAgICAgICAgICAgICBoYW5kbGVEZWxldGUoY2F0ZWdvcnkpXG4gICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC1yZWQtNjAwIGhvdmVyOmJnLXJlZC01MCByb3VuZGVkIHAtMVwiXG4gICAgICAgICAgICAgIHRpdGxlPVwiRGVsZXRlIGNhdGVnb3J5XCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPFRyYXNoSWNvbiBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIClcbiAgfVxuXG4gIGNvbnN0IGdldEFsbENhdGVnb3JpZXMgPSAoY2F0czogQ2F0ZWdvcnlbXSk6IENhdGVnb3J5W10gPT4ge1xuICAgIGxldCBhbGw6IENhdGVnb3J5W10gPSBbXVxuICAgIGNhdHMuZm9yRWFjaChjYXQgPT4ge1xuICAgICAgYWxsLnB1c2goY2F0KVxuICAgICAgaWYgKGNhdC5jaGlsZHJlbiAmJiBjYXQuY2hpbGRyZW4ubGVuZ3RoID4gMCkge1xuICAgICAgICBhbGwgPSBhbGwuY29uY2F0KGdldEFsbENhdGVnb3JpZXMoY2F0LmNoaWxkcmVuKSlcbiAgICAgIH1cbiAgICB9KVxuICAgIHJldHVybiBhbGxcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgIDxDYXRlZ29yeUhlYWRlclxuICAgICAgICB0aXRsZT1cIkNhdGVnb3JpZXNcIlxuICAgICAgICBkZXNjcmlwdGlvbj1cIk1hbmFnZSBzZXJ2aWNlIGNhdGVnb3JpZXMgYW5kIHN1YmNhdGVnb3JpZXNcIlxuICAgICAgICBzZWFyY2hQbGFjZWhvbGRlcj1cIlNlYXJjaCBjYXRlZ29yaWVzIGJ5IG5hbWUgb3IgZGVzY3JpcHRpb24uLi5cIlxuICAgICAgICBzZWFyY2hRdWVyeT17c2VhcmNoUXVlcnl9XG4gICAgICAgIG9uU2VhcmNoQ2hhbmdlPXtzZXRTZWFyY2hRdWVyeX1cbiAgICAgICAgZW5hYmxlU2VhcmNoPXt0cnVlfVxuICAgICAgICBlbmFibGVGaWx0ZXJzPXt0cnVlfVxuICAgICAgICBlbmFibGVWaWV3Q29udHJvbHM9e3RydWV9XG4gICAgICAgIGVuYWJsZUNyZWF0ZT17dHJ1ZX1cbiAgICAgICAgb25DcmVhdGVDbGljaz17aGFuZGxlQ3JlYXRlQ2xpY2t9XG4gICAgICAgIGNyZWF0ZUJ1dHRvblRleHQ9XCJBZGQgQ2F0ZWdvcnlcIlxuICAgICAgICB2aWV3TW9kZT17dmlld01vZGV9XG4gICAgICAgIG9uVmlld01vZGVDaGFuZ2U9e3NldFZpZXdNb2RlfVxuICAgICAgICBmaWx0ZXJzPXtmaWx0ZXJzfVxuICAgICAgICBvbkZpbHRlcnNDaGFuZ2U9e2hhbmRsZUZpbHRlcnNDaGFuZ2V9XG4gICAgICAgIGN1cnJlbnRGaWx0ZXJzPXtjdXJyZW50RmlsdGVyc31cbiAgICAgICAgaXRlbUNvdW50PXtmaWx0ZXJlZENhdGVnb3JpZXMubGVuZ3RofVxuICAgICAgICB0b3RhbEl0ZW1zPXtjYXRlZ29yaWVzLmxlbmd0aH1cbiAgICAgIC8+XG5cbiAgICAgIHsvKiBDYXRlZ29yaWVzIExpc3QgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93LXNtIGJvcmRlciBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAge2xvYWRpbmcgPyAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTYgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTggdy04IGJvcmRlci1iLTIgYm9yZGVyLWJsdWUtNjAwIG14LWF1dG9cIj48L2Rpdj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTIgdGV4dC1ncmF5LTYwMFwiPkxvYWRpbmcgY2F0ZWdvcmllcy4uLjwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKSA6IGZpbHRlcmVkQ2F0ZWdvcmllcy5sZW5ndGggPT09IDAgPyAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTYgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxGb2xkZXJJY29uIGNsYXNzTmFtZT1cImgtMTIgdy0xMiB0ZXh0LWdyYXktNDAwIG14LWF1dG8gbWItNFwiIC8+XG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTJcIj5ObyBjYXRlZ29yaWVzIGZvdW5kPC9oMz5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItNFwiPlxuICAgICAgICAgICAgICB7c2VhcmNoUXVlcnkgfHwgT2JqZWN0LmtleXMoY3VycmVudEZpbHRlcnMpLnNvbWUoa2V5ID0+IGN1cnJlbnRGaWx0ZXJzW2tleV0pIFxuICAgICAgICAgICAgICAgID8gJ1RyeSBhZGp1c3RpbmcgeW91ciBzZWFyY2ggdGVybXMgb3IgZmlsdGVycy4nIFxuICAgICAgICAgICAgICAgIDogJ0dldCBzdGFydGVkIGJ5IGNyZWF0aW5nIHlvdXIgZmlyc3QgY2F0ZWdvcnkuJ31cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQ3JlYXRlQ2xpY2t9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC00IHB5LTIgYmctYmx1ZS02MDAgdGV4dC13aGl0ZSByb3VuZGVkLWxnIGhvdmVyOmJnLWJsdWUtNzAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPFBsdXNJY29uIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgIEFkZCBDYXRlZ29yeVxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICkgOiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTRcIj5cbiAgICAgICAgICAgIHt2aWV3TW9kZSA9PT0gJ3RhYmxlJyAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1ub25lIGJvcmRlciBib3JkZXItZ3JheS0yMDAgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAgICAgICAgPHRhYmxlIGNsYXNzTmFtZT1cIm1pbi13LWZ1bGwgZGl2aWRlLXkgZGl2aWRlLWdyYXktMjAwXCI+XG4gICAgICAgICAgICAgICAgICA8dGhlYWQgY2xhc3NOYW1lPVwiYmctZ3JheS01MFwiPlxuICAgICAgICAgICAgICAgICAgICA8dHI+XG4gICAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTQgcHktMyB0ZXh0LWxlZnQgdGV4dC14cyBmb250LWJvbGQgdGV4dC1ncmF5LTcwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIENhdGVnb3J5XG4gICAgICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNCBweS0zIHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtYm9sZCB0ZXh0LWdyYXktNzAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgUGFyZW50XG4gICAgICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNCBweS0zIHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtYm9sZCB0ZXh0LWdyYXktNzAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgU2VydmljZXNcbiAgICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC00IHB5LTMgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1ib2xkIHRleHQtZ3JheS03MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBTdGF0dXNcbiAgICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC00IHB5LTMgdGV4dC1yaWdodCB0ZXh0LXhzIGZvbnQtYm9sZCB0ZXh0LWdyYXktNzAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgQWN0aW9uc1xuICAgICAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgICAgIDwvdHI+XG4gICAgICAgICAgICAgICAgICA8L3RoZWFkPlxuICAgICAgICAgICAgICAgICAgPHRib2R5IGNsYXNzTmFtZT1cImJnLXdoaXRlIGRpdmlkZS15IGRpdmlkZS1ncmF5LTIwMFwiPlxuICAgICAgICAgICAgICAgICAgICB7Z2V0QWxsQ2F0ZWdvcmllcyhmaWx0ZXJlZENhdGVnb3JpZXMpLm1hcCgoY2F0ZWdvcnkpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8bW90aW9uLnRyXG4gICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2NhdGVnb3J5LmlkfVxuICAgICAgICAgICAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAxMCB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BjdXJzb3ItcG9pbnRlciB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgaG92ZXI6YmctZ3JheS01MCAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICBzZWxlY3RlZENhdGVnb3J5Py5pZCA9PT0gY2F0ZWdvcnkuaWQgPyAnYmctYmx1ZS01MCBib3JkZXItbC00IGJvcmRlci1sLWJsdWUtNTAwJyA6ICcnXG4gICAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IG9uQ2F0ZWdvcnlTZWxlY3QoY2F0ZWdvcnkpfVxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC00IHB5LTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtc2hyaW5rLTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxGb2xkZXJJY29uIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1ibHVlLTUwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4tdy0wIGZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCB0cnVuY2F0ZVwiPntjYXRlZ29yeS5uYW1lfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjYXRlZ29yeS5kZXNjcmlwdGlvbiAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCB0cnVuY2F0ZVwiPntjYXRlZ29yeS5kZXNjcmlwdGlvbn08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNCBweS0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtjYXRlZ29yeS5wYXJlbnRJZCA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjYXRlZ29yaWVzLmZpbmQoYyA9PiBjLmlkID09PSBjYXRlZ29yeS5wYXJlbnRJZCk/Lm5hbWUgfHwgJ1Vua25vd24nfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS00MDAgaXRhbGljXCI+Um9vdCBDYXRlZ29yeTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNCBweS0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC0yIHB5LTEgcm91bmRlZC1ub25lIHRleHQteHMgZm9udC1tZWRpdW0gYmctYmx1ZS0xMDAgdGV4dC1ibHVlLTgwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjYXRlZ29yeS5fY291bnQ/LnNlcnZpY2VzIHx8IDB9IHNlcnZpY2VzXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNCBweS0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YGlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC0yIHB5LTEgcm91bmRlZC1ub25lIHRleHQteHMgZm9udC1tZWRpdW0gJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYXRlZ29yeS5pc0FjdGl2ZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnYmctZ3JlZW4tMTAwIHRleHQtZ3JlZW4tODAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnYmctcmVkLTEwMCB0ZXh0LXJlZC04MDAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y2F0ZWdvcnkuaXNBY3RpdmUgPyAnQWN0aXZlJyA6ICdJbmFjdGl2ZSd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNCBweS0zIHRleHQtcmlnaHRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWVuZCBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZUVkaXQoY2F0ZWdvcnkpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTIgcHktMSBib3JkZXIgYm9yZGVyLXRyYW5zcGFyZW50IHRleHQteHMgZm9udC1tZWRpdW0gcm91bmRlZC1ub25lIHRleHQtd2hpdGUgYmctYmx1ZS02MDAgaG92ZXI6YmctYmx1ZS03MDAgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLW9mZnNldC0yIGZvY3VzOnJpbmctYmx1ZS01MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCJFZGl0IENhdGVnb3J5XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8UGVuY2lsSWNvbiBjbGFzc05hbWU9XCJoLTMgdy0zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZVRvZ2dsZUFjdGl2ZShjYXRlZ29yeSlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtMiBweS0xIGJvcmRlciBib3JkZXItdHJhbnNwYXJlbnQgdGV4dC14cyBmb250LW1lZGl1bSByb3VuZGVkLW5vbmUgdGV4dC13aGl0ZSAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYXRlZ29yeS5pc0FjdGl2ZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLW9yYW5nZS02MDAgaG92ZXI6Ymctb3JhbmdlLTcwMCBmb2N1czpyaW5nLW9yYW5nZS01MDAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnYmctZ3JlZW4tNjAwIGhvdmVyOmJnLWdyZWVuLTcwMCBmb2N1czpyaW5nLWdyZWVuLTUwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0gZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLW9mZnNldC0yYH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPXtjYXRlZ29yeS5pc0FjdGl2ZSA/ICdEZWFjdGl2YXRlIENhdGVnb3J5JyA6ICdBY3RpdmF0ZSBDYXRlZ29yeSd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2NhdGVnb3J5LmlzQWN0aXZlID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8RXllU2xhc2hJY29uIGNsYXNzTmFtZT1cImgtMyB3LTNcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEV5ZUljb24gY2xhc3NOYW1lPVwiaC0zIHctM1wiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlRGVsZXRlKGNhdGVnb3J5KVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC0yIHB5LTEgYm9yZGVyIGJvcmRlci10cmFuc3BhcmVudCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHJvdW5kZWQtbm9uZSB0ZXh0LXdoaXRlIGJnLXJlZC02MDAgaG92ZXI6YmctcmVkLTcwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctb2Zmc2V0LTIgZm9jdXM6cmluZy1yZWQtNTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPVwiRGVsZXRlIENhdGVnb3J5XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VHJhc2hJY29uIGNsYXNzTmFtZT1cImgtMyB3LTNcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgPC9tb3Rpb24udHI+XG4gICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgPC90Ym9keT5cbiAgICAgICAgICAgICAgICA8L3RhYmxlPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHt2aWV3TW9kZSA9PT0gJ2xpc3QnICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICB7ZmlsdGVyZWRDYXRlZ29yaWVzLm1hcCgoY2F0ZWdvcnkpID0+IHJlbmRlckNhdGVnb3J5KGNhdGVnb3J5KSl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAge3ZpZXdNb2RlID09PSAnZ3JpZCcgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ2FwLTQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTMgeGw6Z3JpZC1jb2xzLTRcIj5cbiAgICAgICAgICAgICAgICB7Z2V0QWxsQ2F0ZWdvcmllcyhmaWx0ZXJlZENhdGVnb3JpZXMpLm1hcCgoY2F0ZWdvcnkpID0+IHJlbmRlckNhdGVnb3J5Q2FyZChjYXRlZ29yeSwgZmFsc2UpKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuXG4gICAgICAgICAgICB7dmlld01vZGUgPT09ICdjYXJkJyAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBnYXAtNiBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtM1wiPlxuICAgICAgICAgICAgICAgIHtnZXRBbGxDYXRlZ29yaWVzKGZpbHRlcmVkQ2F0ZWdvcmllcykubWFwKChjYXRlZ29yeSkgPT4gcmVuZGVyQ2F0ZWdvcnlDYXJkKGNhdGVnb3J5LCB0cnVlKSl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogRm9ybSBNb2RhbCAqL31cbiAgICAgIDxBbmltYXRlUHJlc2VuY2U+XG4gICAgICAgIHtpc0Zvcm1PcGVuICYmIChcbiAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwIH19XG4gICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEgfX1cbiAgICAgICAgICAgIGV4aXQ9e3sgb3BhY2l0eTogMCB9fVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBiZy1ibGFjayBiZy1vcGFjaXR5LTUwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHotNTBcIlxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNGb3JtT3BlbihmYWxzZSl9XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgaW5pdGlhbD17eyBzY2FsZTogMC45NSwgb3BhY2l0eTogMCB9fVxuICAgICAgICAgICAgICBhbmltYXRlPXt7IHNjYWxlOiAxLCBvcGFjaXR5OiAxIH19XG4gICAgICAgICAgICAgIGV4aXQ9e3sgc2NhbGU6IDAuOTUsIG9wYWNpdHk6IDAgfX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBwLTYgdy1mdWxsIG1heC13LW1kIG14LTRcIlxuICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4gZS5zdG9wUHJvcGFnYXRpb24oKX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCBtYi00XCI+XG4gICAgICAgICAgICAgICAge2VkaXRpbmdDYXRlZ29yeSA/ICdFZGl0IENhdGVnb3J5JyA6ICdBZGQgQ2F0ZWdvcnknfVxuICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgPGZvcm0gb25TdWJtaXQ9e2FzeW5jIChlKSA9PiB7XG4gICAgICAgICAgICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpXG4gICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAgIGNvbnN0IHVybCA9IGVkaXRpbmdDYXRlZ29yeSBcbiAgICAgICAgICAgICAgICAgICAgPyBgL2FwaS9hZG1pbi9jYXRlZ29yaWVzLyR7ZWRpdGluZ0NhdGVnb3J5LmlkfWBcbiAgICAgICAgICAgICAgICAgICAgOiAnL2FwaS9hZG1pbi9jYXRlZ29yaWVzJ1xuICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICBjb25zdCBtZXRob2QgPSBlZGl0aW5nQ2F0ZWdvcnkgPyAnUFVUJyA6ICdQT1NUJ1xuICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKHVybCwge1xuICAgICAgICAgICAgICAgICAgICBtZXRob2QsXG4gICAgICAgICAgICAgICAgICAgIGhlYWRlcnM6IHsgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyB9LFxuICAgICAgICAgICAgICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgICAgICAgICAgICAgY2F0ZWduYW1lOiBmb3JtRGF0YS5uYW1lLFxuICAgICAgICAgICAgICAgICAgICAgIGNhdGVnZGVzYzogZm9ybURhdGEuZGVzY3JpcHRpb24sXG4gICAgICAgICAgICAgICAgICAgICAgcGFyZW50aWQ6IGZvcm1EYXRhLnBhcmVudElkID8gTnVtYmVyKGZvcm1EYXRhLnBhcmVudElkKSA6IDAsXG4gICAgICAgICAgICAgICAgICAgICAgaXNhY3RpdmU6IGZvcm1EYXRhLmlzQWN0aXZlLFxuICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXlvcmRlcjogZm9ybURhdGEuZGlzcGxheU9yZGVyXG4gICAgICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICAgICAgICB9KVxuXG4gICAgICAgICAgICAgICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgICAgICAgICAgICAgc2V0SXNGb3JtT3BlbihmYWxzZSlcbiAgICAgICAgICAgICAgICAgICAgZmV0Y2hDYXRlZ29yaWVzKClcbiAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGVycm9yRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKVxuICAgICAgICAgICAgICAgICAgICBhbGVydChlcnJvckRhdGEubWVzc2FnZSB8fCAnRmFpbGVkIHRvIHNhdmUgY2F0ZWdvcnknKVxuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBzYXZpbmcgY2F0ZWdvcnk6JywgZXJyb3IpXG4gICAgICAgICAgICAgICAgICBhbGVydCgnQW4gZXJyb3Igb2NjdXJyZWQgd2hpbGUgc2F2aW5nIHRoZSBjYXRlZ29yeScpXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgTmFtZSAqXG4gICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Rm9ybURhdGEoeyAuLi5mb3JtRGF0YSwgbmFtZTogZS50YXJnZXQudmFsdWUgfSl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50XCJcbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICBEZXNjcmlwdGlvblxuICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8dGV4dGFyZWFcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuZGVzY3JpcHRpb259XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGb3JtRGF0YSh7IC4uLmZvcm1EYXRhLCBkZXNjcmlwdGlvbjogZS50YXJnZXQudmFsdWUgfSl9XG4gICAgICAgICAgICAgICAgICAgICAgcm93cz17M31cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1sZyBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpib3JkZXItdHJhbnNwYXJlbnRcIlxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMVwiPlxuICAgICAgICAgICAgICAgICAgICAgIFBhcmVudCBDYXRlZ29yeVxuICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnBhcmVudElkfVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Rm9ybURhdGEoeyAuLi5mb3JtRGF0YSwgcGFyZW50SWQ6IGUudGFyZ2V0LnZhbHVlIH0pfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudFwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiXCI+Tm8gcGFyZW50IChyb290IGNhdGVnb3J5KTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgIHtjYXRlZ29yaWVzLm1hcCgoY2F0KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIGtleT17Y2F0LmlkfSB2YWx1ZT17Y2F0LmlkfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2NhdC5uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e2Zvcm1EYXRhLmlzQWN0aXZlfVxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGb3JtRGF0YSh7IC4uLmZvcm1EYXRhLCBpc0FjdGl2ZTogZS50YXJnZXQuY2hlY2tlZCB9KX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJvdW5kZWQgYm9yZGVyLWdyYXktMzAwIHRleHQtYmx1ZS02MDAgZm9jdXM6cmluZy1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0yIHRleHQtc20gdGV4dC1ncmF5LTcwMFwiPkFjdGl2ZTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICBEaXNwbGF5IE9yZGVyXG4gICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5kaXNwbGF5T3JkZXJ9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGb3JtRGF0YSh7IC4uLmZvcm1EYXRhLCBkaXNwbGF5T3JkZXI6IE51bWJlcihlLnRhcmdldC52YWx1ZSkgfSl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50XCJcbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWVuZCBzcGFjZS14LTMgbXQtNlwiPlxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNGb3JtT3BlbihmYWxzZSl9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTQgcHktMiB0ZXh0LWdyYXktNzAwIGJnLWdyYXktMTAwIHJvdW5kZWQtbGcgaG92ZXI6YmctZ3JheS0yMDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICBDYW5jZWxcbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwic3VibWl0XCJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0yIGJnLWJsdWUtNjAwIHRleHQtd2hpdGUgcm91bmRlZC1sZyBob3ZlcjpiZy1ibHVlLTcwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIHtlZGl0aW5nQ2F0ZWdvcnkgPyAnVXBkYXRlJyA6ICdDcmVhdGUnfVxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZm9ybT5cbiAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICl9XG4gICAgICA8L0FuaW1hdGVQcmVzZW5jZT5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwibW90aW9uIiwiQW5pbWF0ZVByZXNlbmNlIiwiUGx1c0ljb24iLCJQZW5jaWxJY29uIiwiVHJhc2hJY29uIiwiRm9sZGVySWNvbiIsIkZvbGRlck9wZW5JY29uIiwiQ2hldnJvblJpZ2h0SWNvbiIsIkNoZXZyb25Eb3duSWNvbiIsIkV5ZUljb24iLCJFeWVTbGFzaEljb24iLCJDYXRlZ29yeUhlYWRlciIsIkNhdGVnb3J5TWFuYWdlbWVudCIsInNlbGVjdGVkQ2F0ZWdvcnkiLCJvbkNhdGVnb3J5U2VsZWN0IiwiY2F0ZWdvcmllcyIsInNldENhdGVnb3JpZXMiLCJmaWx0ZXJlZENhdGVnb3JpZXMiLCJzZXRGaWx0ZXJlZENhdGVnb3JpZXMiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImlzRm9ybU9wZW4iLCJzZXRJc0Zvcm1PcGVuIiwiZWRpdGluZ0NhdGVnb3J5Iiwic2V0RWRpdGluZ0NhdGVnb3J5IiwiZXhwYW5kZWRDYXRlZ29yaWVzIiwic2V0RXhwYW5kZWRDYXRlZ29yaWVzIiwiU2V0Iiwic2VhcmNoUXVlcnkiLCJzZXRTZWFyY2hRdWVyeSIsImN1cnJlbnRGaWx0ZXJzIiwic2V0Q3VycmVudEZpbHRlcnMiLCJ2aWV3TW9kZSIsInNldFZpZXdNb2RlIiwiZGVuc2l0eSIsInNldERlbnNpdHkiLCJmb3JtRGF0YSIsInNldEZvcm1EYXRhIiwibmFtZSIsImRlc2NyaXB0aW9uIiwicGFyZW50SWQiLCJpc0FjdGl2ZSIsImRpc3BsYXlPcmRlciIsImZpbHRlcnMiLCJrZXkiLCJsYWJlbCIsInR5cGUiLCJvcHRpb25zIiwidmFsdWUiLCJmZXRjaENhdGVnb3JpZXMiLCJmaWx0ZXJBbmRTb3J0Q2F0ZWdvcmllcyIsImZpbHRlcmVkIiwidHJpbSIsInNlYXJjaExvd2VyIiwidG9Mb3dlckNhc2UiLCJmaWx0ZXIiLCJjYXRlZ29yeSIsImluY2x1ZGVzIiwic3RhdHVzIiwicGFyZW50IiwiYnVpbGRDYXRlZ29yeVRyZWUiLCJmbGF0Q2F0ZWdvcmllcyIsImNhdGVnb3J5TWFwIiwiTWFwIiwicm9vdENhdGVnb3JpZXMiLCJmb3JFYWNoIiwiY2F0IiwiaWQiLCJTdHJpbmciLCJjYXRlZ25hbWUiLCJjYXRlZ2Rlc2MiLCJwYXJlbnRpZCIsInVuZGVmaW5lZCIsImlzYWN0aXZlIiwiZGlzcGxheW9yZGVyIiwiY2hpbGRyZW4iLCJfY291bnQiLCJzZXQiLCJoYXMiLCJnZXQiLCJwdXNoIiwic29ydENhdGVnb3JpZXMiLCJjYXRzIiwic29ydCIsImEiLCJiIiwicmVzcG9uc2UiLCJmZXRjaCIsIm9rIiwiZGF0YSIsImpzb24iLCJjYXRlZ29yaWVzRGF0YSIsImNvbnNvbGUiLCJlcnJvciIsInN0YXR1c1RleHQiLCJoYW5kbGVFZGl0IiwiaGFuZGxlRGVsZXRlIiwiY29uZmlybSIsIm1ldGhvZCIsImVycm9yRGF0YSIsImFsZXJ0IiwibWVzc2FnZSIsImhhbmRsZVRvZ2dsZUFjdGl2ZSIsImhlYWRlcnMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsIk51bWJlciIsInRvZ2dsZUV4cGFuZGVkIiwiY2F0ZWdvcnlJZCIsIm5ld0V4cGFuZGVkIiwiZGVsZXRlIiwiYWRkIiwicmVuZGVyQ2F0ZWdvcnkiLCJsZXZlbCIsImlzRXhwYW5kZWQiLCJoYXNDaGlsZHJlbiIsImxlbmd0aCIsImlzU2VsZWN0ZWQiLCJkaXYiLCJjbGFzc05hbWUiLCJzdHlsZSIsIm1hcmdpbkxlZnQiLCJvbkNsaWNrIiwiYnV0dG9uIiwiZSIsInN0b3BQcm9wYWdhdGlvbiIsImgzIiwicCIsInNlcnZpY2VzIiwic3BhbiIsInRpdGxlIiwibWFwIiwiY2hpbGQiLCJoYW5kbGVDcmVhdGVDbGljayIsImhhbmRsZUZpbHRlcnNDaGFuZ2UiLCJuZXdGaWx0ZXJzIiwicmVuZGVyQ2F0ZWdvcnlDYXJkIiwiaXNMYXJnZUNhcmQiLCJnZXRBbGxDYXRlZ29yaWVzIiwiYWxsIiwiY29uY2F0Iiwic2VhcmNoUGxhY2Vob2xkZXIiLCJvblNlYXJjaENoYW5nZSIsImVuYWJsZVNlYXJjaCIsImVuYWJsZUZpbHRlcnMiLCJlbmFibGVWaWV3Q29udHJvbHMiLCJlbmFibGVDcmVhdGUiLCJvbkNyZWF0ZUNsaWNrIiwiY3JlYXRlQnV0dG9uVGV4dCIsIm9uVmlld01vZGVDaGFuZ2UiLCJvbkZpbHRlcnNDaGFuZ2UiLCJpdGVtQ291bnQiLCJ0b3RhbEl0ZW1zIiwiT2JqZWN0Iiwia2V5cyIsInNvbWUiLCJ0YWJsZSIsInRoZWFkIiwidHIiLCJ0aCIsInRib2R5IiwiaW5pdGlhbCIsIm9wYWNpdHkiLCJ5IiwiYW5pbWF0ZSIsInRkIiwiZmluZCIsImMiLCJleGl0Iiwic2NhbGUiLCJmb3JtIiwib25TdWJtaXQiLCJwcmV2ZW50RGVmYXVsdCIsInVybCIsImlucHV0IiwicmVxdWlyZWQiLCJvbkNoYW5nZSIsInRhcmdldCIsInRleHRhcmVhIiwicm93cyIsInNlbGVjdCIsIm9wdGlvbiIsImNoZWNrZWQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/services/category-management.tsx\n"));

/***/ })

});