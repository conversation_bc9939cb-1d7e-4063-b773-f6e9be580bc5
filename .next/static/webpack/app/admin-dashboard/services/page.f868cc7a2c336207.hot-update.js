"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/services/page",{

/***/ "(app-pages-browser)/./src/components/admin/services/option-features-management.tsx":
/*!**********************************************************************!*\
  !*** ./src/components/admin/services/option-features-management.tsx ***!
  \**********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OptionFeaturesManagement: () => (/* binding */ OptionFeaturesManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,StarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,StarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,StarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,StarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,StarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,StarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,StarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,StarIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* __next_internal_client_entry_do_not_use__ OptionFeaturesManagement auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction OptionFeaturesManagement(param) {\n    let { option } = param;\n    _s();\n    const [features, setFeatures] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredFeatures, setFilteredFeatures] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isFormOpen, setIsFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingFeature, setEditingFeature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [debouncedSearchTerm, setDebouncedSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        description: '',\n        isIncluded: true,\n        isHighlighted: false,\n        displayOrder: 0\n    });\n    // Debounce search term\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OptionFeaturesManagement.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"OptionFeaturesManagement.useEffect.timer\": ()=>{\n                    setDebouncedSearchTerm(searchTerm);\n                }\n            }[\"OptionFeaturesManagement.useEffect.timer\"], 300);\n            return ({\n                \"OptionFeaturesManagement.useEffect\": ()=>clearTimeout(timer)\n            })[\"OptionFeaturesManagement.useEffect\"];\n        }\n    }[\"OptionFeaturesManagement.useEffect\"], [\n        searchTerm\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OptionFeaturesManagement.useEffect\": ()=>{\n            fetchFeatures();\n        }\n    }[\"OptionFeaturesManagement.useEffect\"], [\n        option.id\n    ]);\n    // Mock data for demonstration\n    const fetchFeatures = async ()=>{\n        setLoading(true);\n        try {\n            // Mock data - replace with actual API call\n            const mockFeatures = [\n                {\n                    id: '1',\n                    optionId: option.id,\n                    name: 'Responsive Design',\n                    description: 'Mobile-friendly design that works on all devices',\n                    isIncluded: true,\n                    isHighlighted: true,\n                    displayOrder: 1,\n                    createdAt: '2024-01-15T10:00:00Z',\n                    updatedAt: '2024-01-15T10:00:00Z',\n                    option: {\n                        id: option.id,\n                        name: option.name\n                    }\n                },\n                {\n                    id: '2',\n                    optionId: option.id,\n                    name: 'SEO Optimization',\n                    description: 'Search engine optimization for better visibility',\n                    isIncluded: true,\n                    isHighlighted: false,\n                    displayOrder: 2,\n                    createdAt: '2024-01-14T10:00:00Z',\n                    updatedAt: '2024-01-14T10:00:00Z',\n                    option: {\n                        id: option.id,\n                        name: option.name\n                    }\n                },\n                {\n                    id: '3',\n                    optionId: option.id,\n                    name: 'Analytics Integration',\n                    description: 'Google Analytics and tracking setup',\n                    isIncluded: true,\n                    isHighlighted: false,\n                    displayOrder: 3,\n                    createdAt: '2024-01-13T10:00:00Z',\n                    updatedAt: '2024-01-13T10:00:00Z',\n                    option: {\n                        id: option.id,\n                        name: option.name\n                    }\n                },\n                {\n                    id: '4',\n                    optionId: option.id,\n                    name: 'Premium Support',\n                    description: '24/7 priority customer support',\n                    isIncluded: false,\n                    isHighlighted: true,\n                    displayOrder: 4,\n                    createdAt: '2024-01-12T10:00:00Z',\n                    updatedAt: '2024-01-12T10:00:00Z',\n                    option: {\n                        id: option.id,\n                        name: option.name\n                    }\n                }\n            ];\n            setFeatures(mockFeatures);\n            setFilteredFeatures(mockFeatures);\n        } catch (error) {\n            console.error('Error fetching features:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleCreateFeature = ()=>{\n        setIsFormOpen(true);\n        setEditingFeature(null);\n        setFormData({\n            name: '',\n            description: '',\n            isIncluded: true,\n            isHighlighted: false,\n            displayOrder: 0\n        });\n    };\n    const handleEditFeature = (feature)=>{\n        setEditingFeature(feature);\n        setFormData({\n            name: feature.name,\n            description: feature.description || '',\n            isIncluded: feature.isIncluded,\n            isHighlighted: feature.isHighlighted,\n            displayOrder: feature.displayOrder\n        });\n        setIsFormOpen(true);\n    };\n    const handleDeleteFeature = async (featureId)=>{\n        if (confirm('Are you sure you want to delete this feature?')) {\n            try {\n                // Mock delete - replace with actual API call\n                setFeatures((prev)=>prev.filter((feature)=>feature.id !== featureId));\n                setFilteredFeatures((prev)=>prev.filter((feature)=>feature.id !== featureId));\n            } catch (error) {\n                console.error('Error deleting feature:', error);\n            }\n        }\n    };\n    const handleSubmitForm = async (e)=>{\n        e.preventDefault();\n        try {\n            if (editingFeature) {\n                // Update existing feature\n                const updatedFeature = {\n                    ...editingFeature,\n                    ...formData\n                };\n                setFeatures((prev)=>prev.map((feature)=>feature.id === editingFeature.id ? updatedFeature : feature));\n                setFilteredFeatures((prev)=>prev.map((feature)=>feature.id === editingFeature.id ? updatedFeature : feature));\n            } else {\n                // Create new feature\n                const newFeature = {\n                    id: Date.now().toString(),\n                    optionId: option.id,\n                    ...formData,\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString(),\n                    option: {\n                        id: option.id,\n                        name: option.name\n                    }\n                };\n                setFeatures((prev)=>[\n                        ...prev,\n                        newFeature\n                    ]);\n                setFilteredFeatures((prev)=>[\n                        ...prev,\n                        newFeature\n                    ]);\n            }\n            setIsFormOpen(false);\n            setEditingFeature(null);\n        } catch (error) {\n            console.error('Error saving feature:', error);\n        }\n    };\n    const toggleFeatureIncluded = async (featureId)=>{\n        try {\n            setFeatures((prev)=>prev.map((feature)=>feature.id === featureId ? {\n                        ...feature,\n                        isIncluded: !feature.isIncluded\n                    } : feature));\n            setFilteredFeatures((prev)=>prev.map((feature)=>feature.id === featureId ? {\n                        ...feature,\n                        isIncluded: !feature.isIncluded\n                    } : feature));\n        } catch (error) {\n            console.error('Error toggling feature:', error);\n        }\n    };\n    const toggleFeatureHighlighted = async (featureId)=>{\n        try {\n            setFeatures((prev)=>prev.map((feature)=>feature.id === featureId ? {\n                        ...feature,\n                        isHighlighted: !feature.isHighlighted\n                    } : feature));\n            setFilteredFeatures((prev)=>prev.map((feature)=>feature.id === featureId ? {\n                        ...feature,\n                        isHighlighted: !feature.isHighlighted\n                    } : feature));\n        } catch (error) {\n            console.error('Error toggling feature highlight:', error);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-200 rounded w-1/4 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            ...Array(5)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-12 bg-gray-200 rounded\"\n                            }, i, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                lineNumber: 264,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n            lineNumber: 263,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900\",\n                                children: \"Option Features\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    \"Manage features for \",\n                                    option.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleCreateFeature,\n                        className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 11\n                            }, this),\n                            \"Add Feature\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                lineNumber: 279,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 flex flex-col sm:flex-row gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"Search features...\",\n                                value: searchTerm,\n                                onChange: (e)=>setSearchTerm(e.target.value),\n                                className: \"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{},\n                        className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 11\n                            }, this),\n                            \"Filters\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                lineNumber: 294,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-none border border-gray-200 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"min-w-full divide-y divide-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Feature\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Type\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-right text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"bg-white divide-y divide-gray-200\",\n                            children: filteredFeatures.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.tr, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 10\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    className: \"transition-all duration-200 hover:bg-gray-50 \".concat(feature.isHighlighted ? 'bg-purple-50 border-l-4 border-l-purple-500' : feature.isIncluded ? 'bg-green-50 border-l-4 border-l-green-500' : 'bg-gray-50'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fas fa-star text-base \".concat(feature.isHighlighted ? 'text-purple-500' : 'text-gray-400')\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"min-w-0 flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs font-bold text-gray-900 truncate\",\n                                                                children: feature.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                                lineNumber: 355,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            feature.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 truncate\",\n                                                                children: feature.description\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                                lineNumber: 357,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-2 py-1 rounded-none text-xs font-medium \".concat(feature.isIncluded ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                                children: feature.isIncluded ? 'Included' : 'Not Included'\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-3\",\n                                            children: feature.isHighlighted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-2 py-1 rounded-none text-xs font-medium bg-purple-100 text-purple-800\",\n                                                children: \"Featured\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-3 text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-end space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>toggleFeatureIncluded(feature.id),\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white \".concat(feature.isIncluded ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500' : 'bg-green-600 hover:bg-green-700 focus:ring-green-500', \" focus:outline-none focus:ring-2 focus:ring-offset-2\"),\n                                                        title: feature.isIncluded ? 'Mark as not included' : 'Mark as included',\n                                                        children: feature.isIncluded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>toggleFeatureHighlighted(feature.id),\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white \".concat(feature.isHighlighted ? 'bg-gray-600 hover:bg-gray-700 focus:ring-gray-500' : 'bg-purple-600 hover:bg-purple-700 focus:ring-purple-500', \" focus:outline-none focus:ring-2 focus:ring-offset-2\"),\n                                                        title: feature.isHighlighted ? 'Remove highlight' : 'Highlight feature',\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                            lineNumber: 404,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleEditFeature(feature),\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                                        title: \"Edit Feature\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                            lineNumber: 411,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 406,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleDeleteFeature(feature.id),\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                                                        title: \"Delete Feature\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                            lineNumber: 418,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, feature.id, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                            lineNumber: 333,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                    lineNumber: 316,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                lineNumber: 315,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.AnimatePresence, {\n                children: isFormOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                    onClick: ()=>setIsFormOpen(false),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            scale: 0.95,\n                            opacity: 0\n                        },\n                        animate: {\n                            scale: 1,\n                            opacity: 1\n                        },\n                        exit: {\n                            scale: 0.95,\n                            opacity: 0\n                        },\n                        className: \"bg-white rounded-none p-6 w-full max-w-md mx-4\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: editingFeature ? 'Edit Feature' : 'Create Feature'\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmitForm,\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Name\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: formData.name,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        name: e.target.value\n                                                    }),\n                                                className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Description\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: formData.description,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        description: e.target.value\n                                                    }),\n                                                className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-purple-500 focus:border-purple-500\",\n                                                rows: 3\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: formData.isIncluded,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                isIncluded: e.target.checked\n                                                            }),\n                                                        className: \"h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 470,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"ml-2 block text-sm text-gray-900\",\n                                                        children: \"Included\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: formData.isHighlighted,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                isHighlighted: e.target.checked\n                                                            }),\n                                                        className: \"h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"ml-2 block text-sm text-gray-900\",\n                                                        children: \"Highlighted\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                        lineNumber: 485,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>setIsFormOpen(false),\n                                                className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50\",\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"submit\",\n                                                className: \"px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-md hover:bg-purple-700\",\n                                                children: editingFeature ? 'Update' : 'Create'\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                        lineNumber: 488,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                        lineNumber: 438,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                    lineNumber: 431,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n                lineNumber: 429,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/option-features-management.tsx\",\n        lineNumber: 277,\n        columnNumber: 5\n    }, this);\n}\n_s(OptionFeaturesManagement, \"u4vEeTm7gkLRoZ/E2xEtVDzNOYQ=\");\n_c = OptionFeaturesManagement;\nvar _c;\n$RefreshReg$(_c, \"OptionFeaturesManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/services/option-features-management.tsx\n"));

/***/ })

});