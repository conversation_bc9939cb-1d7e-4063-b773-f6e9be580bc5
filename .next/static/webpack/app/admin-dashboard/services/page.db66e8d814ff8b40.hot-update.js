"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/services/page",{

/***/ "(app-pages-browser)/./src/components/admin/services/service-options-management.tsx":
/*!**********************************************************************!*\
  !*** ./src/components/admin/services/service-options-management.tsx ***!
  \**********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ServiceOptionsManagement: () => (/* binding */ ServiceOptionsManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* __next_internal_client_entry_do_not_use__ ServiceOptionsManagement auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ServiceOptionsManagement(param) {\n    let { service, selectedOption, onOptionSelect } = param;\n    _s();\n    const [options, setOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredOptions, setFilteredOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isFormOpen, setIsFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingOption, setEditingOption] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [debouncedSearchTerm, setDebouncedSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('list');\n    // Function to get option-specific icons based on option name/type\n    const getOptionIcon = (option)=>{\n        const name = option.name.toLowerCase();\n        // Basic/Standard packages\n        if (name.includes('basic') || name.includes('starter') || name.includes('essential')) {\n            return 'fa-layer-group text-blue-500';\n        }\n        // Premium/Pro packages\n        if (name.includes('premium') || name.includes('pro') || name.includes('professional') || name.includes('advanced')) {\n            return 'fa-crown text-yellow-500';\n        }\n        // Enterprise/Business packages\n        if (name.includes('enterprise') || name.includes('business') || name.includes('corporate')) {\n            return 'fa-building text-purple-500';\n        }\n        // Custom/Bespoke options\n        if (name.includes('custom') || name.includes('bespoke') || name.includes('tailored')) {\n            return 'fa-magic text-pink-500';\n        }\n        // Maintenance/Support options\n        if (name.includes('maintenance') || name.includes('support') || name.includes('care')) {\n            return 'fa-tools text-orange-500';\n        }\n        // Hosting/Domain options\n        if (name.includes('hosting') || name.includes('domain') || name.includes('server')) {\n            return 'fa-server text-gray-500';\n        }\n        // SEO/Marketing options\n        if (name.includes('seo') || name.includes('marketing') || name.includes('promotion')) {\n            return 'fa-search text-green-500';\n        }\n        // Security options\n        if (name.includes('security') || name.includes('ssl') || name.includes('protection')) {\n            return 'fa-shield-alt text-red-500';\n        }\n        // Analytics/Tracking options\n        if (name.includes('analytics') || name.includes('tracking') || name.includes('monitoring')) {\n            return 'fa-chart-bar text-teal-500';\n        }\n        // Default\n        return 'fa-list-ul text-orange-500';\n    };\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        description: '',\n        price: 0,\n        discountRate: 0,\n        isActive: true\n    });\n    // Debounce search term\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ServiceOptionsManagement.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"ServiceOptionsManagement.useEffect.timer\": ()=>{\n                    setDebouncedSearchTerm(searchTerm);\n                }\n            }[\"ServiceOptionsManagement.useEffect.timer\"], 300);\n            return ({\n                \"ServiceOptionsManagement.useEffect\": ()=>clearTimeout(timer)\n            })[\"ServiceOptionsManagement.useEffect\"];\n        }\n    }[\"ServiceOptionsManagement.useEffect\"], [\n        searchTerm\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ServiceOptionsManagement.useEffect\": ()=>{\n            fetchOptions();\n        }\n    }[\"ServiceOptionsManagement.useEffect\"], [\n        service.id\n    ]);\n    // Mock data for demonstration\n    const fetchOptions = async ()=>{\n        setLoading(true);\n        try {\n            // Mock data - replace with actual API call\n            const mockOptions = [\n                {\n                    id: '1',\n                    serviceId: service.id,\n                    name: 'Basic Package',\n                    description: 'Essential features for small businesses',\n                    price: 1000,\n                    discountRate: 5,\n                    totalDiscount: 50,\n                    isActive: true,\n                    createdAt: '2024-01-15T10:00:00Z',\n                    updatedAt: '2024-01-15T10:00:00Z',\n                    service: {\n                        id: service.id,\n                        name: service.name,\n                        category: service.category\n                    },\n                    _count: {\n                        features: 5,\n                        orderDetails: 12\n                    }\n                },\n                {\n                    id: '2',\n                    serviceId: service.id,\n                    name: 'Professional Package',\n                    description: 'Advanced features for growing businesses',\n                    price: 2500,\n                    discountRate: 10,\n                    totalDiscount: 250,\n                    isActive: true,\n                    createdAt: '2024-01-14T10:00:00Z',\n                    updatedAt: '2024-01-14T10:00:00Z',\n                    service: {\n                        id: service.id,\n                        name: service.name,\n                        category: service.category\n                    },\n                    _count: {\n                        features: 12,\n                        orderDetails: 8\n                    }\n                },\n                {\n                    id: '3',\n                    serviceId: service.id,\n                    name: 'Enterprise Package',\n                    description: 'Complete solution for large organizations',\n                    price: 5000,\n                    discountRate: 15,\n                    totalDiscount: 750,\n                    isActive: true,\n                    createdAt: '2024-01-13T10:00:00Z',\n                    updatedAt: '2024-01-13T10:00:00Z',\n                    service: {\n                        id: service.id,\n                        name: service.name,\n                        category: service.category\n                    },\n                    _count: {\n                        features: 20,\n                        orderDetails: 15\n                    }\n                }\n            ];\n            setOptions(mockOptions);\n            setFilteredOptions(mockOptions);\n        } catch (error) {\n            console.error('Error fetching options:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleOptionSelect = (option)=>{\n        onOptionSelect(option);\n    };\n    const handleCreateOption = ()=>{\n        setIsFormOpen(true);\n        setEditingOption(null);\n        setFormData({\n            name: '',\n            description: '',\n            price: 0,\n            discountRate: 0,\n            isActive: true\n        });\n    };\n    const handleEditOption = (option)=>{\n        setEditingOption(option);\n        setFormData({\n            name: option.name,\n            description: option.description || '',\n            price: option.price || 0,\n            discountRate: option.discountRate || 0,\n            isActive: option.isActive\n        });\n        setIsFormOpen(true);\n    };\n    const handleDeleteOption = async (optionId)=>{\n        if (confirm('Are you sure you want to delete this option?')) {\n            try {\n                // Mock delete - replace with actual API call\n                setOptions((prev)=>prev.filter((option)=>option.id !== optionId));\n                setFilteredOptions((prev)=>prev.filter((option)=>option.id !== optionId));\n            } catch (error) {\n                console.error('Error deleting option:', error);\n            }\n        }\n    };\n    const handleSubmitForm = async (e)=>{\n        e.preventDefault();\n        try {\n            if (editingOption) {\n                // Update existing option\n                const updatedOption = {\n                    ...editingOption,\n                    ...formData\n                };\n                setOptions((prev)=>prev.map((option)=>option.id === editingOption.id ? updatedOption : option));\n                setFilteredOptions((prev)=>prev.map((option)=>option.id === editingOption.id ? updatedOption : option));\n            } else {\n                // Create new option\n                const newOption = {\n                    id: Date.now().toString(),\n                    serviceId: service.id,\n                    ...formData,\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString(),\n                    service: {\n                        id: service.id,\n                        name: service.name,\n                        category: service.category\n                    },\n                    _count: {\n                        features: 0,\n                        orderDetails: 0\n                    }\n                };\n                setOptions((prev)=>[\n                        ...prev,\n                        newOption\n                    ]);\n                setFilteredOptions((prev)=>[\n                        ...prev,\n                        newOption\n                    ]);\n            }\n            setIsFormOpen(false);\n            setEditingOption(null);\n        } catch (error) {\n            console.error('Error saving option:', error);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-200 rounded w-1/4 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            ...Array(5)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-12 bg-gray-200 rounded\"\n                            }, i, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                lineNumber: 282,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n            lineNumber: 281,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900\",\n                                children: \"Service Options\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    \"Manage options for \",\n                                    service.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleCreateOption,\n                        className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 11\n                            }, this),\n                            \"Add Option\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                lineNumber: 297,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 flex flex-col sm:flex-row gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"Search options...\",\n                                value: searchTerm,\n                                onChange: (e)=>setSearchTerm(e.target.value),\n                                className: \"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{},\n                        className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 11\n                            }, this),\n                            \"Filters\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                lineNumber: 312,\n                columnNumber: 7\n            }, this),\n            viewMode === 'list' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 border border-gray-200 rounded-none px-4 py-3 mb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Option\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Price\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Features\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-32\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                        lineNumber: 336,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1\",\n                        children: filteredOptions.map((option)=>{\n                            var _option__count;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 10\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                className: \"flex items-center py-2 px-4 rounded-none cursor-pointer transition-all duration-200 border border-gray-200 \".concat((selectedOption === null || selectedOption === void 0 ? void 0 : selectedOption.id) === option.id ? 'bg-orange-50 border-orange-300' : 'bg-white hover:bg-gray-50'),\n                                onClick: ()=>handleOptionSelect(option),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas \".concat(getOptionIcon(option), \" text-base\")\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-bold text-base truncate \".concat((selectedOption === null || selectedOption === void 0 ? void 0 : selectedOption.id) === option.id ? 'text-orange-900' : 'text-gray-900'),\n                                                children: option.name\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 19\n                                            }, this),\n                                            option.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm truncate \".concat((selectedOption === null || selectedOption === void 0 ? void 0 : selectedOption.id) === option.id ? 'text-orange-600' : 'text-gray-600'),\n                                                children: option.description\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-24\",\n                                        children: [\n                                            option.price && option.price > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs font-bold text-orange-600\",\n                                                children: [\n                                                    \"$\",\n                                                    option.price.toLocaleString()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs font-bold text-gray-400\",\n                                                children: \"Free\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 21\n                                            }, this),\n                                            option.discountRate && option.discountRate > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-red-600 font-medium\",\n                                                children: [\n                                                    option.discountRate,\n                                                    \"% off\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2 py-1 rounded-none text-xs font-medium bg-purple-100 text-purple-800\",\n                                            children: ((_option__count = option._count) === null || _option__count === void 0 ? void 0 : _option__count.features) || 0\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                            lineNumber: 406,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2 py-1 rounded-none text-xs font-medium \".concat(option.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                            children: option.isActive ? 'Active' : 'Inactive'\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 w-32\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    handleEditOption(option);\n                                                },\n                                                className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500\",\n                                                title: \"Edit Option\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    handleDeleteOption(option.id);\n                                                },\n                                                className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                                                title: \"Delete Option\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, option.id, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                lineNumber: 334,\n                columnNumber: 9\n            }, this),\n            viewMode === 'table' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-none border border-gray-200 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"min-w-full divide-y divide-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Option\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Price\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Features\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                        lineNumber: 462,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-right text-xs font-bold text-gray-700 uppercase tracking-wider\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                lineNumber: 455,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                            lineNumber: 454,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"bg-white divide-y divide-gray-200\",\n                            children: filteredOptions.map((option)=>{\n                                var _option__count;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.tr, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 10\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    className: \"cursor-pointer transition-all duration-200 hover:bg-gray-50 \".concat((selectedOption === null || selectedOption === void 0 ? void 0 : selectedOption.id) === option.id ? 'bg-orange-50 border-l-4 border-l-orange-500' : ''),\n                                    onClick: ()=>handleOptionSelect(option),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fas \".concat(getOptionIcon(option), \" text-base\")\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                            lineNumber: 487,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                        lineNumber: 486,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"min-w-0 flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-bold text-gray-900 truncate\",\n                                                                children: option.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                                lineNumber: 490,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            option.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500 truncate\",\n                                                                children: option.description\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                                lineNumber: 492,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col\",\n                                                children: [\n                                                    option.price && option.price > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-bold text-orange-600\",\n                                                        children: [\n                                                            \"$\",\n                                                            option.price.toLocaleString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                        lineNumber: 500,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-bold text-gray-400\",\n                                                        children: \"Free\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                        lineNumber: 504,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    option.discountRate && option.discountRate > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-red-600 font-medium\",\n                                                        children: [\n                                                            option.discountRate,\n                                                            \"% off\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                            lineNumber: 497,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-2 py-1 rounded-none text-xs font-medium bg-purple-100 text-purple-800\",\n                                                children: [\n                                                    ((_option__count = option._count) === null || _option__count === void 0 ? void 0 : _option__count.features) || 0,\n                                                    \" features\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                lineNumber: 514,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                            lineNumber: 513,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-2 py-1 rounded-none text-xs font-medium \".concat(option.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                                children: option.isActive ? 'Active' : 'Inactive'\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                lineNumber: 519,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                            lineNumber: 518,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-1 text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-end space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            handleEditOption(option);\n                                                        },\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500\",\n                                                        title: \"Edit Option\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                            lineNumber: 537,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                        lineNumber: 529,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            handleDeleteOption(option.id);\n                                                        },\n                                                        className: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-none text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                                                        title: \"Delete Option\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                            lineNumber: 547,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                        lineNumber: 539,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                lineNumber: 528,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, option.id, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                    lineNumber: 475,\n                                    columnNumber: 15\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                            lineNumber: 473,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                    lineNumber: 453,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                lineNumber: 452,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                children: isFormOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                    onClick: ()=>setIsFormOpen(false),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            scale: 0.95,\n                            opacity: 0\n                        },\n                        animate: {\n                            scale: 1,\n                            opacity: 1\n                        },\n                        exit: {\n                            scale: 0.95,\n                            opacity: 0\n                        },\n                        className: \"bg-white rounded-none p-6 w-full max-w-md mx-4\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: editingOption ? 'Edit Option' : 'Create Option'\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                lineNumber: 575,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmitForm,\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Name\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                lineNumber: 580,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: formData.name,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        name: e.target.value\n                                                    }),\n                                                className: \"mt-1 block w-full border border-gray-300 rounded-none px-3 py-2 focus:outline-none focus:ring-orange-500 focus:border-orange-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                lineNumber: 581,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                        lineNumber: 579,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Description\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                lineNumber: 590,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: formData.description,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        description: e.target.value\n                                                    }),\n                                                className: \"mt-1 block w-full border border-gray-300 rounded-none px-3 py-2 focus:outline-none focus:ring-orange-500 focus:border-orange-500\",\n                                                rows: 3\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                lineNumber: 591,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                        lineNumber: 589,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700\",\n                                                        children: \"Price\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                        lineNumber: 600,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: formData.price,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                price: parseFloat(e.target.value) || 0\n                                                            }),\n                                                        className: \"mt-1 block w-full border border-gray-300 rounded-none px-3 py-2 focus:outline-none focus:ring-orange-500 focus:border-orange-500\",\n                                                        min: \"0\",\n                                                        step: \"0.01\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                        lineNumber: 601,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                lineNumber: 599,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700\",\n                                                        children: \"Discount Rate (%)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                        lineNumber: 611,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: formData.discountRate,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                discountRate: parseFloat(e.target.value) || 0\n                                                            }),\n                                                        className: \"mt-1 block w-full border border-gray-300 rounded-none px-3 py-2 focus:outline-none focus:ring-orange-500 focus:border-orange-500\",\n                                                        min: \"0\",\n                                                        max: \"100\",\n                                                        step: \"0.1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                        lineNumber: 612,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                lineNumber: 610,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                        lineNumber: 598,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: formData.isActive,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        isActive: e.target.checked\n                                                    }),\n                                                className: \"h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded-none\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"ml-2 block text-sm text-gray-900\",\n                                                children: \"Active\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                lineNumber: 630,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                        lineNumber: 623,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>setIsFormOpen(false),\n                                                className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-none hover:bg-gray-50\",\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                lineNumber: 633,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"submit\",\n                                                className: \"px-4 py-2 text-sm font-medium text-white bg-orange-600 border border-transparent rounded-none hover:bg-orange-700\",\n                                                children: editingOption ? 'Update' : 'Create'\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                                lineNumber: 640,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                        lineNumber: 632,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                                lineNumber: 578,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                        lineNumber: 568,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                    lineNumber: 561,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n                lineNumber: 559,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/services/service-options-management.tsx\",\n        lineNumber: 295,\n        columnNumber: 5\n    }, this);\n}\n_s(ServiceOptionsManagement, \"DM5g/dwwMXQ6N0qdpKyVN2rrVTc=\");\n_c = ServiceOptionsManagement;\nvar _c;\n$RefreshReg$(_c, \"ServiceOptionsManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/services/service-options-management.tsx\n"));

/***/ })

});