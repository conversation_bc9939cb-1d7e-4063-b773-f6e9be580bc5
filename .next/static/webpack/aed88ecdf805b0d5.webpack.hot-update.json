{"c": ["app/layout", "app/admin-dashboard/services/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FolderIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FolderOpenIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway%2Fsrc%2Fcomponents%2Fadmin%2Fservices%2Fservices-management.tsx%22%2C%22ids%22%3A%5B%22ServicesManagement%22%5D%7D&server=false!", "(app-pages-browser)/./src/components/admin/services/category-header.tsx", "(app-pages-browser)/./src/components/admin/services/category-management.tsx", "(app-pages-browser)/./src/components/admin/services/option-features-management.tsx", "(app-pages-browser)/./src/components/admin/services/service-management.tsx", "(app-pages-browser)/./src/components/admin/services/service-options-management.tsx", "(app-pages-browser)/./src/components/admin/services/services-management.tsx"]}